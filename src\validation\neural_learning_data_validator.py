"""
Neural Learning Data Validator - Ensures AI components train exclusively on actual trading outcomes
"""

import time
import logging
import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Union, Tuple
from decimal import Decimal
import json
import hashlib
import numpy as np

logger = logging.getLogger(__name__)

class NeuralLearningDataValidator:
    """
    Validates that AI components train exclusively on actual trading outcomes and real market movements.
    Prohibits synthetic training data and ensures all learning is based on authentic market experiences.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.validation_enabled = True
        self.fail_fast_mode = True
        self.max_training_data_age_hours = 24  # Training data should be recent
        
        # Track validation statistics
        self.training_validations = 0
        self.validation_failures = 0
        self.validated_models = set()
        
        # Prohibited synthetic data patterns
        self.synthetic_indicators = {
            'data_sources': [
                'synthetic', 'generated', 'simulated', 'artificial', 'mock', 'fake',
                'random', 'gaussian', 'uniform', 'normal_distribution', 'monte_carlo'
            ],
            'feature_patterns': [
                'synthetic_', 'generated_', 'simulated_', 'mock_', 'fake_',
                'random_', 'test_', 'dummy_', 'placeholder_'
            ],
            'outcome_patterns': [
                'simulated_profit', 'fake_loss', 'test_outcome', 'mock_result',
                'generated_return', 'synthetic_pnl'
            ]
        }
        
        # Required authentic data sources
        self.authentic_sources = {
            'trading_outcomes': ['actual_trades', 'real_executions', 'live_orders'],
            'market_data': ['live_prices', 'real_volume', 'actual_orderbook'],
            'performance_metrics': ['actual_pnl', 'real_returns', 'live_performance']
        }
        
        # Training data validators by type
        self.training_validators = {
            'trading_outcomes': self._validate_trading_outcomes,
            'market_movements': self._validate_market_movements,
            'performance_data': self._validate_performance_data,
            'feature_data': self._validate_feature_data,
            'reward_signals': self._validate_reward_signals
        }
        
        logger.info("🔒 [NEURAL-LEARNING-VALIDATOR] Initialized with real-outcomes-only validation")
    
    async def validate_training_data(self, training_data: Dict[str, Any], 
                                   model_name: str, training_type: str) -> Tuple[bool, Dict[str, Any]]:
        """
        Validate training data is based on actual trading outcomes
        
        Returns:
            Tuple[bool, Dict[str, Any]]: (is_valid, validation_details)
        """
        validation_details = {
            'model_name': model_name,
            'training_type': training_type,
            'validation_timestamp': time.time(),
            'checks_performed': [],
            'warnings': [],
            'errors': [],
            'data_summary': {}
        }
        
        try:
            self.training_validations += 1
            
            # 1. Validate data authenticity
            if not self._validate_data_authenticity(training_data, validation_details):
                return False, validation_details
            
            # 2. Validate data freshness
            if not self._validate_training_data_freshness(training_data, validation_details):
                return False, validation_details
            
            # 3. Validate data source traceability
            if not self._validate_data_source_traceability(training_data, validation_details):
                return False, validation_details
            
            # 4. Training type specific validation
            if not await self._perform_training_type_validation(
                training_data, training_type, validation_details):
                return False, validation_details
            
            # 5. Validate outcome authenticity
            if not self._validate_outcome_authenticity(training_data, validation_details):
                return False, validation_details
            
            # 6. Validate learning consistency
            if not self._validate_learning_consistency(training_data, validation_details):
                return False, validation_details
            
            # 7. Validate against synthetic patterns
            if not self._validate_no_synthetic_patterns(training_data, validation_details):
                return False, validation_details
            
            validation_details['status'] = 'VALID'
            self.validated_models.add(model_name)
            logger.info(f"✅ [NEURAL-LEARNING-VALIDATOR] Training data validated for {model_name}")
            return True, validation_details
            
        except Exception as e:
            validation_details['errors'].append(f"Validation exception: {str(e)}")
            validation_details['status'] = 'ERROR'
            self.validation_failures += 1
            
            if self.fail_fast_mode:
                logger.error(f"❌ [NEURAL-LEARNING-VALIDATOR] Training validation failed: {e}")
                raise RuntimeError(f"CRITICAL: Neural learning data validation failed - {e}")
            else:
                logger.warning(f"⚠️ [NEURAL-LEARNING-VALIDATOR] Training validation warning: {e}")
                return False, validation_details
    
    def _validate_data_authenticity(self, training_data: Dict[str, Any], 
                                  validation_details: Dict[str, Any]) -> bool:
        """Validate training data is authentic and not synthetic"""
        validation_details['checks_performed'].append('data_authenticity')
        
        # Check for explicit authenticity markers
        authenticity_markers = training_data.get('authenticity', {})
        if authenticity_markers:
            is_real = authenticity_markers.get('is_real_data', False)
            if not is_real:
                validation_details['errors'].append("Training data marked as non-real")
                return False
            
            data_source = authenticity_markers.get('data_source', '').lower()
            if any(indicator in data_source for indicator in self.synthetic_indicators['data_sources']):
                validation_details['errors'].append(f"Synthetic data source: {data_source}")
                return False
        
        # Check data structure for authenticity indicators
        if 'metadata' in training_data:
            metadata = training_data['metadata']
            if isinstance(metadata, dict):
                source_type = metadata.get('source_type', '').lower()
                if source_type in self.synthetic_indicators['data_sources']:
                    validation_details['errors'].append(f"Synthetic source type: {source_type}")
                    return False
        
        return True
    
    def _validate_training_data_freshness(self, training_data: Dict[str, Any], 
                                        validation_details: Dict[str, Any]) -> bool:
        """Validate training data is recent enough"""
        validation_details['checks_performed'].append('data_freshness')
        
        # Extract data timestamp
        timestamp = self._extract_training_timestamp(training_data)
        if not timestamp:
            validation_details['warnings'].append("No timestamp found in training data")
            return True  # Allow if no timestamp
        
        current_time = time.time()
        age_hours = (current_time - timestamp) / 3600
        
        if age_hours > self.max_training_data_age_hours:
            validation_details['warnings'].append(
                f"Training data is old: {age_hours:.1f}h > {self.max_training_data_age_hours}h"
            )
        
        validation_details['data_age_hours'] = age_hours
        return True
    
    def _validate_data_source_traceability(self, training_data: Dict[str, Any], 
                                         validation_details: Dict[str, Any]) -> bool:
        """Validate training data has proper source traceability"""
        validation_details['checks_performed'].append('source_traceability')
        
        # Check for data lineage
        lineage = training_data.get('lineage') or training_data.get('provenance')
        if not lineage:
            validation_details['warnings'].append("No data lineage information found")
            return True
        
        # Validate lineage contains authentic sources
        if isinstance(lineage, dict):
            sources = lineage.get('sources', [])
            if sources:
                for source in sources:
                    source_str = str(source).lower()
                    if any(indicator in source_str for indicator in self.synthetic_indicators['data_sources']):
                        validation_details['errors'].append(f"Synthetic source in lineage: {source}")
                        return False
        
        validation_details['data_lineage'] = lineage
        return True
    
    async def _perform_training_type_validation(self, training_data: Dict[str, Any], 
                                              training_type: str, validation_details: Dict[str, Any]) -> bool:
        """Perform training type specific validation"""
        validation_details['checks_performed'].append('training_type_specific')
        
        training_type_lower = training_type.lower()
        if training_type_lower in self.training_validators:
            return await self.training_validators[training_type_lower](training_data, validation_details)
        else:
            validation_details['warnings'].append(f"No specific validator for training type: {training_type}")
            return True
    
    async def _validate_trading_outcomes(self, training_data: Dict[str, Any], 
                                       validation_details: Dict[str, Any]) -> bool:
        """Validate trading outcomes are from actual trades"""
        outcomes = training_data.get('outcomes', [])
        if not outcomes:
            validation_details['errors'].append("No trading outcomes found")
            return False
        
        for i, outcome in enumerate(outcomes):
            if isinstance(outcome, dict):
                # Check for transaction ID (indicates real trade)
                tx_id = outcome.get('transaction_id') or outcome.get('order_id')
                if not tx_id:
                    validation_details['warnings'].append(f"No transaction ID in outcome {i}")
                
                # Check for realistic profit/loss values
                pnl = outcome.get('pnl') or outcome.get('profit_loss')
                if pnl is not None:
                    try:
                        pnl_float = float(pnl)
                        # Check for obviously fake PnL values
                        if pnl_float in [100.0, 1000.0, -100.0, -1000.0]:
                            validation_details['warnings'].append(f"Suspicious PnL value: {pnl_float}")
                    except (ValueError, TypeError):
                        validation_details['warnings'].append(f"Invalid PnL format: {pnl}")
        
        validation_details['data_summary']['outcome_count'] = len(outcomes)
        return True
    
    async def _validate_market_movements(self, training_data: Dict[str, Any], 
                                       validation_details: Dict[str, Any]) -> bool:
        """Validate market movements are from real market data"""
        movements = training_data.get('market_movements', [])
        if not movements:
            validation_details['warnings'].append("No market movements found")
            return True
        
        for i, movement in enumerate(movements):
            if isinstance(movement, dict):
                # Check for realistic price movements
                price_change = movement.get('price_change')
                if price_change is not None:
                    try:
                        change_float = float(price_change)
                        # Check for unrealistic price changes
                        if abs(change_float) > 0.5:  # More than 50% change
                            validation_details['warnings'].append(
                                f"Large price change at {i}: {change_float:.2%}"
                            )
                    except (ValueError, TypeError):
                        validation_details['warnings'].append(f"Invalid price change: {price_change}")
                
                # Check for volume data (indicates real market activity)
                volume = movement.get('volume')
                if volume is not None and float(volume) <= 0:
                    validation_details['warnings'].append(f"Invalid volume at {i}: {volume}")
        
        validation_details['data_summary']['movement_count'] = len(movements)
        return True
    
    async def _validate_performance_data(self, training_data: Dict[str, Any], 
                                       validation_details: Dict[str, Any]) -> bool:
        """Validate performance data is from actual trading performance"""
        performance = training_data.get('performance', {})
        if not performance:
            validation_details['warnings'].append("No performance data found")
            return True
        
        # Check for realistic performance metrics
        metrics = ['total_return', 'sharpe_ratio', 'max_drawdown', 'win_rate']
        for metric in metrics:
            if metric in performance:
                value = performance[metric]
                try:
                    value_float = float(value)
                    
                    # Validate metric ranges
                    if metric == 'win_rate' and (value_float < 0 or value_float > 1):
                        validation_details['warnings'].append(f"Invalid win rate: {value_float}")
                    elif metric == 'max_drawdown' and value_float > 0:
                        validation_details['warnings'].append(f"Invalid max drawdown: {value_float}")
                    elif metric == 'sharpe_ratio' and abs(value_float) > 10:
                        validation_details['warnings'].append(f"Unrealistic Sharpe ratio: {value_float}")
                        
                except (ValueError, TypeError):
                    validation_details['warnings'].append(f"Invalid {metric} format: {value}")
        
        return True
    
    async def _validate_feature_data(self, training_data: Dict[str, Any], 
                                   validation_details: Dict[str, Any]) -> bool:
        """Validate feature data is derived from real market data"""
        features = training_data.get('features', {})
        if not features:
            validation_details['warnings'].append("No feature data found")
            return True
        
        # Check feature names for synthetic patterns
        for feature_name in features.keys():
            feature_name_lower = feature_name.lower()
            for pattern in self.synthetic_indicators['feature_patterns']:
                if pattern in feature_name_lower:
                    validation_details['errors'].append(f"Synthetic feature pattern: {feature_name}")
                    return False
        
        # Check feature values for realism
        for feature_name, feature_values in features.items():
            if isinstance(feature_values, (list, tuple, np.ndarray)):
                # Check for constant values (often indicates synthetic data)
                if len(set(feature_values)) == 1:
                    validation_details['warnings'].append(f"Constant feature values: {feature_name}")
                
                # Check for NaN or infinite values
                for value in feature_values:
                    if isinstance(value, float) and (np.isnan(value) or np.isinf(value)):
                        validation_details['warnings'].append(f"Invalid feature value in {feature_name}")
                        break
        
        validation_details['data_summary']['feature_count'] = len(features)
        return True
    
    async def _validate_reward_signals(self, training_data: Dict[str, Any], 
                                     validation_details: Dict[str, Any]) -> bool:
        """Validate reward signals are based on actual trading results"""
        rewards = training_data.get('rewards', [])
        if not rewards:
            validation_details['warnings'].append("No reward signals found")
            return True
        
        for i, reward in enumerate(rewards):
            try:
                reward_float = float(reward)
                
                # Check for obviously fake reward values
                if reward_float in [1.0, -1.0, 0.5, -0.5, 10.0, -10.0]:
                    validation_details['warnings'].append(f"Suspicious reward value: {reward_float}")
                
                # Check for extreme rewards
                if abs(reward_float) > 100:
                    validation_details['warnings'].append(f"Extreme reward at {i}: {reward_float}")
                    
            except (ValueError, TypeError):
                validation_details['warnings'].append(f"Invalid reward format at {i}: {reward}")
        
        validation_details['data_summary']['reward_count'] = len(rewards)
        return True
    
    def _validate_outcome_authenticity(self, training_data: Dict[str, Any], 
                                     validation_details: Dict[str, Any]) -> bool:
        """Validate outcomes are authentic and not synthetic"""
        validation_details['checks_performed'].append('outcome_authenticity')
        
        # Check for outcome patterns that indicate synthetic data
        data_str = json.dumps(training_data, default=str).lower()
        
        for pattern in self.synthetic_indicators['outcome_patterns']:
            if pattern in data_str:
                validation_details['errors'].append(f"Synthetic outcome pattern: {pattern}")
                return False
        
        return True
    
    def _validate_learning_consistency(self, training_data: Dict[str, Any], 
                                     validation_details: Dict[str, Any]) -> bool:
        """Validate learning data is internally consistent"""
        validation_details['checks_performed'].append('learning_consistency')
        
        # Check for consistent data sizes
        data_sizes = {}
        for key, value in training_data.items():
            if isinstance(value, (list, tuple, np.ndarray)):
                data_sizes[key] = len(value)
        
        if data_sizes:
            unique_sizes = set(data_sizes.values())
            if len(unique_sizes) > 1:
                validation_details['warnings'].append(f"Inconsistent data sizes: {data_sizes}")
        
        return True
    
    def _validate_no_synthetic_patterns(self, training_data: Dict[str, Any], 
                                      validation_details: Dict[str, Any]) -> bool:
        """Validate training data contains no synthetic patterns"""
        validation_details['checks_performed'].append('synthetic_patterns')
        
        # Convert training data to string for pattern matching
        data_str = json.dumps(training_data, default=str).lower()
        
        # Check for synthetic indicators
        for category, patterns in self.synthetic_indicators.items():
            for pattern in patterns:
                if pattern in data_str:
                    validation_details['errors'].append(f"Synthetic pattern found ({category}): {pattern}")
                    return False
        
        return True
    
    def _extract_training_timestamp(self, training_data: Dict[str, Any]) -> Optional[float]:
        """Extract timestamp from training data"""
        timestamp_fields = ['timestamp', 'created_at', 'training_time', 'data_time']
        
        for field in timestamp_fields:
            if field in training_data:
                timestamp = training_data[field]
                try:
                    if isinstance(timestamp, (int, float)):
                        if timestamp > 1e12:  # Milliseconds
                            return timestamp / 1000
                        else:  # Seconds
                            return float(timestamp)
                    elif isinstance(timestamp, str):
                        dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                        return dt.timestamp()
                except (ValueError, TypeError):
                    continue
        
        return None
    
    async def validate_model_learning_session(self, session_data: Dict[str, Any], 
                                            model_name: str) -> Tuple[bool, Dict[str, Any]]:
        """Validate an entire learning session for a model"""
        validation_details = {
            'model_name': model_name,
            'session_validation_timestamp': time.time(),
            'session_checks': [],
            'warnings': [],
            'errors': []
        }
        
        try:
            # Validate session metadata
            if 'session_metadata' in session_data:
                metadata = session_data['session_metadata']
                if metadata.get('data_type') == 'synthetic':
                    validation_details['errors'].append("Session marked as synthetic data")
                    return False, validation_details
            
            # Validate all training batches in session
            training_batches = session_data.get('training_batches', [])
            for i, batch in enumerate(training_batches):
                batch_valid, batch_details = await self.validate_training_data(
                    batch, model_name, f"batch_{i}"
                )
                if not batch_valid:
                    validation_details['errors'].append(f"Batch {i} validation failed: {batch_details}")
                    return False, validation_details
            
            validation_details['status'] = 'VALID'
            validation_details['validated_batches'] = len(training_batches)
            return True, validation_details
            
        except Exception as e:
            validation_details['errors'].append(f"Session validation exception: {str(e)}")
            if self.fail_fast_mode:
                raise RuntimeError(f"CRITICAL: Learning session validation failed - {e}")
            return False, validation_details
    
    def get_learning_validation_statistics(self) -> Dict[str, Any]:
        """Get neural learning validation statistics"""
        success_rate = ((self.training_validations - self.validation_failures) / 
                       max(self.training_validations, 1)) * 100
        
        return {
            'total_training_validations': self.training_validations,
            'validation_failures': self.validation_failures,
            'success_rate_percent': round(success_rate, 2),
            'validator_status': 'ACTIVE' if self.validation_enabled else 'DISABLED',
            'fail_fast_mode': self.fail_fast_mode,
            'max_training_data_age_hours': self.max_training_data_age_hours,
            'validated_models_count': len(self.validated_models),
            'validated_models': list(self.validated_models),
            'supported_training_types': list(self.training_validators.keys())
        }
