#!/usr/bin/env python3
"""
Simple Bybit API diagnostic script to identify the connection issue
"""
import os
import sys
import time
import requests
from dotenv import load_dotenv

def check_network_connectivity():
    """Test basic network connectivity"""
    print("🌐 [NETWORK] Testing basic network connectivity...")
    
    try:
        # Test general internet
        response = requests.get('https://httpbin.org/ip', timeout=5)
        current_ip = response.json().get('origin', 'Unknown')
        print(f"✅ [NETWORK] Internet connection OK, IP: {current_ip}")
        
        # Test Bybit API endpoint
        response = requests.get('https://api.bybit.com/v5/market/time', timeout=10)
        if response.status_code == 200:
            server_time = response.json()
            print(f"✅ [BYBIT-API] Bybit API endpoint reachable, server time: {server_time}")
            return True
        else:
            print(f"❌ [BYBIT-API] Bybit API endpoint returned status: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ [NETWORK] Network connectivity test failed: {e}")
        return False

def check_credentials():
    """Check if credentials are properly loaded"""
    print("\n🔑 [CREDENTIALS] Checking API credentials...")
    
    load_dotenv()
    
    api_key = os.getenv('BYBIT_API_KEY')
    api_secret = os.getenv('BYBIT_API_SECRET')
    
    if not api_key:
        print("❌ [CREDENTIALS] BYBIT_API_KEY not found in environment")
        return False, None, None
        
    if not api_secret:
        print("❌ [CREDENTIALS] BYBIT_API_SECRET not found in environment")
        return False, None, None
    
    print(f"✅ [CREDENTIALS] API Key found: {api_key[:8]}... (length: {len(api_key)})")
    print(f"✅ [CREDENTIALS] API Secret found: {api_secret[:8]}... (length: {len(api_secret)})")
    
    return True, api_key, api_secret

def test_pybit_import():
    """Test pybit library import"""
    print("\n📦 [PYBIT] Testing pybit library import...")
    
    try:
        from pybit.unified_trading import HTTP
        print("✅ [PYBIT] pybit library imported successfully")
        return True, HTTP
    except ImportError as e:
        print(f"❌ [PYBIT] Failed to import pybit: {e}")
        return False, None

def test_simple_session_creation(api_key, api_secret, HTTP):
    """Test creating a simple Bybit session"""
    print("\n🔧 [SESSION] Testing Bybit session creation...")
    
    try:
        # Create session with minimal configuration
        session = HTTP(
            api_key=api_key,
            api_secret=api_secret,
            testnet=False,
            recv_window=5000
        )
        print("✅ [SESSION] Bybit session created successfully")
        return True, session
    except Exception as e:
        print(f"❌ [SESSION] Failed to create Bybit session: {e}")
        return False, None

def test_account_info(session):
    """Test getting account info"""
    print("\n📊 [ACCOUNT] Testing account info retrieval...")
    
    try:
        print("🔄 [ACCOUNT] Calling get_account_info()...")
        result = session.get_account_info()
        
        print(f"📋 [ACCOUNT] Raw response: {result}")
        
        if isinstance(result, dict):
            ret_code = result.get('retCode', 'Unknown')
            ret_msg = result.get('retMsg', 'Unknown')
            
            if ret_code == 0:
                print("✅ [ACCOUNT] Account info retrieved successfully")
                return True
            else:
                print(f"❌ [ACCOUNT] API returned error - Code: {ret_code}, Message: {ret_msg}")
                return False
        else:
            print(f"❌ [ACCOUNT] Unexpected response format: {type(result)}")
            return False
            
    except Exception as e:
        print(f"❌ [ACCOUNT] Account info test failed: {e}")
        return False

def main():
    """Run comprehensive Bybit diagnostic"""
    print("🔍 BYBIT API DIAGNOSTIC TOOL")
    print("=" * 50)
    
    # Step 1: Network connectivity
    if not check_network_connectivity():
        print("\n❌ DIAGNOSTIC FAILED: Network connectivity issues")
        return False
    
    # Step 2: Credentials
    creds_ok, api_key, api_secret = check_credentials()
    if not creds_ok:
        print("\n❌ DIAGNOSTIC FAILED: Credential issues")
        return False
    
    # Step 3: pybit import
    import_ok, HTTP = test_pybit_import()
    if not import_ok:
        print("\n❌ DIAGNOSTIC FAILED: pybit library issues")
        return False
    
    # Step 4: Session creation
    session_ok, session = test_simple_session_creation(api_key, api_secret, HTTP)
    if not session_ok:
        print("\n❌ DIAGNOSTIC FAILED: Session creation issues")
        return False
    
    # Step 5: Account info test
    account_ok = test_account_info(session)
    if not account_ok:
        print("\n❌ DIAGNOSTIC FAILED: Account info retrieval issues")
        return False
    
    print("\n✅ ALL DIAGNOSTICS PASSED - Bybit API connection is working!")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
