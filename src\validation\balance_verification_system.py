"""
Balance Verification System - Real-time balance validation with live API fetching
"""

import time
import logging
import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Union, Tuple
from decimal import Decimal
import json
import hashlib

logger = logging.getLogger(__name__)

class BalanceVerificationSystem:
    """
    Real-time balance verification system that ensures all balance data comes from live API calls.
    Rejects cached/hardcoded balances and implements strict 10-second freshness requirement.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.validation_enabled = True
        self.fail_fast_mode = True
        self.max_balance_age_seconds = 10  # Balance data must be fresher than 10 seconds
        self.min_api_call_interval = 1  # Minimum 1 second between API calls
        
        # Track balance validation
        self.balance_checks = 0
        self.validation_failures = 0
        self.last_balance_fetch = {}  # Track last fetch time per exchange
        self.balance_cache = {}  # Temporary cache with timestamps
        
        # Prohibited hardcoded balance values
        self.prohibited_balances = {
            'exact_values': [
                10000.0, 1000.0, 100.0, 10.0, 1.0, 0.1, 0.01, 0.001,
                50000.0, 3000.0, 2500.0, 45000.0,  # Common fallback values
                0.027579, 1.375641, 25.79, 34.72, 0.0003794, 17.53789  # Specific hardcoded values
            ],
            'test_patterns': [
                r'^\d+\.0+$',  # Round numbers like 100.000
                r'^0\.0+1$',   # Values like 0.001
                r'^\d{1,3}\.0+$'  # Simple round values
            ]
        }
        
        # Exchange-specific balance validation
        self.exchange_validators = {
            'bybit': self._validate_bybit_balance,
            'coinbase': self._validate_coinbase_balance,
            'binance': self._validate_binance_balance
        }
        
        logger.info("🔒 [BALANCE-VERIFICATION] Initialized with 10-second freshness requirement")
    
    async def verify_balance_authenticity(self, balance_data: Dict[str, Any], exchange: str,
                                        fetch_timestamp: float) -> Tuple[bool, Dict[str, Any]]:
        """
        Verify balance data is authentic and fresh from live API
        
        Returns:
            Tuple[bool, Dict[str, Any]]: (is_valid, verification_details)
        """
        verification_details = {
            'exchange': exchange,
            'fetch_timestamp': fetch_timestamp,
            'verification_timestamp': time.time(),
            'checks_performed': [],
            'warnings': [],
            'errors': [],
            'balance_summary': {}
        }
        
        try:
            self.balance_checks += 1
            
            # 1. Validate balance freshness
            if not self._validate_balance_freshness(balance_data, fetch_timestamp, verification_details):
                return False, verification_details
            
            # 2. Validate balance structure
            if not self._validate_balance_structure(balance_data, exchange, verification_details):
                return False, verification_details
            
            # 3. Validate against hardcoded values
            if not self._validate_no_hardcoded_values(balance_data, verification_details):
                return False, verification_details
            
            # 4. Exchange-specific validation
            if not await self._perform_exchange_specific_validation(balance_data, exchange, verification_details):
                return False, verification_details
            
            # 5. Validate balance consistency
            if not self._validate_balance_consistency(balance_data, verification_details):
                return False, verification_details
            
            # 6. Validate API call frequency
            if not self._validate_api_call_frequency(exchange, fetch_timestamp, verification_details):
                return False, verification_details
            
            # 7. Store verified balance with timestamp
            self._store_verified_balance(balance_data, exchange, fetch_timestamp)
            
            verification_details['status'] = 'VALID'
            logger.debug(f"✅ [BALANCE-VERIFICATION] Balance verified for {exchange}")
            return True, verification_details
            
        except Exception as e:
            verification_details['errors'].append(f"Verification exception: {str(e)}")
            verification_details['status'] = 'ERROR'
            self.validation_failures += 1
            
            if self.fail_fast_mode:
                logger.error(f"❌ [BALANCE-VERIFICATION] Balance verification failed for {exchange}: {e}")
                raise RuntimeError(f"CRITICAL: Balance verification failed - {e}")
            else:
                logger.warning(f"⚠️ [BALANCE-VERIFICATION] Balance verification warning for {exchange}: {e}")
                return False, verification_details
    
    def _validate_balance_freshness(self, balance_data: Dict[str, Any], fetch_timestamp: float,
                                  verification_details: Dict[str, Any]) -> bool:
        """Validate balance data is fresh enough"""
        verification_details['checks_performed'].append('balance_freshness')
        
        current_time = time.time()
        
        # Check fetch timestamp age
        fetch_age = current_time - fetch_timestamp
        if fetch_age > self.max_balance_age_seconds:
            verification_details['errors'].append(
                f"Balance fetch too old: {fetch_age:.1f}s > {self.max_balance_age_seconds}s"
            )
            return False
        
        # Check balance data timestamp if available
        balance_timestamp = self._extract_balance_timestamp(balance_data)
        if balance_timestamp:
            balance_age = current_time - balance_timestamp
            if balance_age > self.max_balance_age_seconds:
                verification_details['errors'].append(
                    f"Balance data too old: {balance_age:.1f}s > {self.max_balance_age_seconds}s"
                )
                return False
            verification_details['balance_timestamp'] = balance_timestamp
        
        verification_details['fetch_age_seconds'] = fetch_age
        return True
    
    def _validate_balance_structure(self, balance_data: Dict[str, Any], exchange: str,
                                  verification_details: Dict[str, Any]) -> bool:
        """Validate balance data structure"""
        verification_details['checks_performed'].append('balance_structure')
        
        # Check basic structure
        if not isinstance(balance_data, dict):
            verification_details['errors'].append("Balance data is not a dictionary")
            return False
        
        # Check for balance information
        balance_fields = ['balances', 'balance', 'result', 'data']
        has_balance_data = any(field in balance_data for field in balance_fields)
        
        if not has_balance_data:
            verification_details['errors'].append("No balance information found in data")
            return False
        
        # Extract and summarize balances
        balances = self._extract_balances(balance_data)
        if not balances:
            verification_details['errors'].append("No balances extracted from data")
            return False
        
        verification_details['balance_summary'] = {
            'currency_count': len(balances),
            'non_zero_balances': len([b for b in balances.values() if float(b) > 0]),
            'total_currencies': list(balances.keys())
        }
        
        return True
    
    def _validate_no_hardcoded_values(self, balance_data: Dict[str, Any],
                                    verification_details: Dict[str, Any]) -> bool:
        """Validate balance data contains no hardcoded values"""
        verification_details['checks_performed'].append('hardcoded_values')
        
        balances = self._extract_balances(balance_data)
        
        for currency, amount in balances.items():
            try:
                amount_float = float(amount)
                
                # Check exact prohibited values
                if amount_float in self.prohibited_balances['exact_values']:
                    verification_details['errors'].append(
                        f"Hardcoded balance detected: {currency}={amount_float}"
                    )
                    return False
                
                # Check for suspicious patterns
                amount_str = str(amount_float)
                for pattern in self.prohibited_balances['test_patterns']:
                    import re
                    if re.match(pattern, amount_str):
                        verification_details['warnings'].append(
                            f"Suspicious balance pattern: {currency}={amount_float}"
                        )
                
            except (ValueError, TypeError):
                verification_details['warnings'].append(f"Non-numeric balance: {currency}={amount}")
        
        return True
    
    async def _perform_exchange_specific_validation(self, balance_data: Dict[str, Any], exchange: str,
                                                  verification_details: Dict[str, Any]) -> bool:
        """Perform exchange-specific balance validation"""
        verification_details['checks_performed'].append('exchange_specific')
        
        exchange_lower = exchange.lower()
        if exchange_lower in self.exchange_validators:
            return await self.exchange_validators[exchange_lower](balance_data, verification_details)
        else:
            verification_details['warnings'].append(f"No specific validator for exchange: {exchange}")
            return True
    
    async def _validate_bybit_balance(self, balance_data: Dict[str, Any],
                                    verification_details: Dict[str, Any]) -> bool:
        """Validate Bybit-specific balance format"""
        # Check for Bybit response structure
        if 'retCode' in balance_data:
            if balance_data['retCode'] != 0:
                verification_details['errors'].append(f"Bybit API error: {balance_data.get('retMsg', 'Unknown')}")
                return False
        
        # Check for Bybit balance structure
        if 'result' in balance_data:
            result = balance_data['result']
            if 'list' in result:
                # Unified account balance format
                return True
            elif 'coin' in result:
                # Spot account balance format
                return True
        
        # Check for direct balance data
        if 'list' in balance_data or 'coin' in balance_data:
            return True
        
        verification_details['warnings'].append("Bybit balance structure not recognized")
        return True
    
    async def _validate_coinbase_balance(self, balance_data: Dict[str, Any],
                                       verification_details: Dict[str, Any]) -> bool:
        """Validate Coinbase-specific balance format"""
        # Check for Coinbase error structure
        if 'errors' in balance_data and balance_data['errors']:
            verification_details['errors'].append(f"Coinbase API errors: {balance_data['errors']}")
            return False
        
        # Check for Coinbase balance structure
        if 'data' in balance_data:
            return True
        
        # Check for direct balance data
        if any(field in balance_data for field in ['available', 'hold', 'balance']):
            return True
        
        verification_details['warnings'].append("Coinbase balance structure not recognized")
        return True
    
    async def _validate_binance_balance(self, balance_data: Dict[str, Any],
                                      verification_details: Dict[str, Any]) -> bool:
        """Validate Binance-specific balance format"""
        # Check for Binance error structure
        if 'code' in balance_data and balance_data['code'] != 200:
            verification_details['errors'].append(f"Binance API error: {balance_data.get('msg', 'Unknown')}")
            return False
        
        # Check for Binance balance structure
        if 'balances' in balance_data:
            return True
        
        verification_details['warnings'].append("Binance balance structure not recognized")
        return True
    
    def _validate_balance_consistency(self, balance_data: Dict[str, Any],
                                    verification_details: Dict[str, Any]) -> bool:
        """Validate balance data is internally consistent"""
        verification_details['checks_performed'].append('balance_consistency')
        
        balances = self._extract_balances(balance_data)
        
        for currency, amount in balances.items():
            try:
                amount_float = float(amount)
                
                # Check for negative balances (usually not allowed)
                if amount_float < 0:
                    verification_details['warnings'].append(f"Negative balance: {currency}={amount_float}")
                
                # Check for extremely large balances (potential error)
                if amount_float > 1e12:
                    verification_details['warnings'].append(f"Extremely large balance: {currency}={amount_float}")
                
            except (ValueError, TypeError):
                verification_details['warnings'].append(f"Invalid balance format: {currency}={amount}")
        
        return True
    
    def _validate_api_call_frequency(self, exchange: str, fetch_timestamp: float,
                                   verification_details: Dict[str, Any]) -> bool:
        """Validate API calls are not too frequent (rate limiting protection)"""
        verification_details['checks_performed'].append('api_call_frequency')
        
        last_fetch = self.last_balance_fetch.get(exchange, 0)
        time_since_last = fetch_timestamp - last_fetch
        
        if time_since_last < self.min_api_call_interval:
            verification_details['warnings'].append(
                f"API calls too frequent: {time_since_last:.1f}s < {self.min_api_call_interval}s"
            )
        
        self.last_balance_fetch[exchange] = fetch_timestamp
        return True
    
    def _store_verified_balance(self, balance_data: Dict[str, Any], exchange: str, timestamp: float):
        """Store verified balance with timestamp"""
        self.balance_cache[exchange] = {
            'data': balance_data,
            'timestamp': timestamp,
            'verification_time': time.time()
        }
        
        # Clean old cache entries
        current_time = time.time()
        for ex in list(self.balance_cache.keys()):
            if current_time - self.balance_cache[ex]['timestamp'] > self.max_balance_age_seconds * 2:
                del self.balance_cache[ex]
    
    def _extract_balances(self, balance_data: Dict[str, Any]) -> Dict[str, Union[float, Decimal]]:
        """Extract balance information from various exchange formats"""
        balances = {}
        
        # Try different balance data structures
        if 'balances' in balance_data:
            # Standard format
            for balance in balance_data['balances']:
                if isinstance(balance, dict):
                    asset = balance.get('asset') or balance.get('currency') or balance.get('coin')
                    amount = balance.get('free') or balance.get('available') or balance.get('balance')
                    if asset and amount is not None:
                        balances[asset] = amount
        
        elif 'result' in balance_data:
            # Bybit format
            result = balance_data['result']
            if 'list' in result:
                for item in result['list']:
                    if 'coin' in item:
                        for coin_data in item['coin']:
                            coin = coin_data.get('coin')
                            amount = coin_data.get('walletBalance') or coin_data.get('availableBalance')
                            if coin and amount is not None:
                                balances[coin] = amount
        
        elif 'data' in balance_data:
            # Coinbase format
            data = balance_data['data']
            if isinstance(data, list):
                for account in data:
                    currency = account.get('currency')
                    amount = account.get('available') or account.get('balance')
                    if currency and amount is not None:
                        balances[currency] = amount
        
        return balances
    
    def _extract_balance_timestamp(self, balance_data: Dict[str, Any]) -> Optional[float]:
        """Extract timestamp from balance data"""
        timestamp_fields = ['timestamp', 'time', 'updateTime', 'created_at', 'updated_at']
        
        for field in timestamp_fields:
            if field in balance_data:
                timestamp = balance_data[field]
                try:
                    if isinstance(timestamp, (int, float)):
                        if timestamp > 1e12:  # Milliseconds
                            return timestamp / 1000
                        else:  # Seconds
                            return float(timestamp)
                    elif isinstance(timestamp, str):
                        dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                        return dt.timestamp()
                except (ValueError, TypeError):
                    continue
        
        return None
    
    async def force_fresh_balance_fetch(self, exchange: str) -> bool:
        """Force a fresh balance fetch if cached data is stale"""
        cached_balance = self.balance_cache.get(exchange)
        if not cached_balance:
            return True  # No cache, fetch needed
        
        age = time.time() - cached_balance['timestamp']
        if age > self.max_balance_age_seconds:
            logger.info(f"🔄 [BALANCE-VERIFICATION] Forcing fresh balance fetch for {exchange} (age: {age:.1f}s)")
            return True
        
        return False  # Cache is still fresh
    
    def get_verification_statistics(self) -> Dict[str, Any]:
        """Get balance verification statistics"""
        success_rate = ((self.balance_checks - self.validation_failures) / max(self.balance_checks, 1)) * 100
        
        return {
            'total_balance_checks': self.balance_checks,
            'validation_failures': self.validation_failures,
            'success_rate_percent': round(success_rate, 2),
            'validator_status': 'ACTIVE' if self.validation_enabled else 'DISABLED',
            'fail_fast_mode': self.fail_fast_mode,
            'max_balance_age_seconds': self.max_balance_age_seconds,
            'cached_exchanges': list(self.balance_cache.keys()),
            'prohibited_balance_count': len(self.prohibited_balances['exact_values'])
        }
