"""
Secure Credentials Management for Exchange APIs
Provides encryption/decryption for sensitive API credentials
"""

import os
import base64
import logging
from typing import Optional, Union
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

logger = logging.getLogger(__name__)

class SecureCredentials:
    """Secure credentials manager with encryption/decryption capabilities"""
    
    def __init__(self, password: Optional[str] = None):
        """Initialize with optional password for encryption"""
        self.password = password or os.getenv('CREDENTIALS_PASSWORD', 'default_password')
        self._fernet = None
    
    def _get_fernet(self) -> Fernet:
        """Get or create Fernet encryption instance"""
        if self._fernet is None:
            try:
                # First try to load the key from the fernet.key file
                key_file_path = os.path.join(os.path.dirname(__file__), 'fernet.key')
                if os.path.exists(key_file_path):
                    with open(key_file_path, 'rb') as key_file:
                        key = key_file.read()
                    self._fernet = Fernet(key)
                    logger.info("🔐 [CRYPTO] Using Fernet key from file")
                else:
                    # Fallback to password-based key derivation
                    password_bytes = self.password.encode()
                    salt = b'stable_salt_for_consistency'  # In production, use random salt

                    kdf = PBKDF2HMAC(
                        algorithm=hashes.SHA256(),
                        length=32,
                        salt=salt,
                        iterations=100000,
                    )

                    key = base64.urlsafe_b64encode(kdf.derive(password_bytes))
                    self._fernet = Fernet(key)
                    logger.info("🔐 [CRYPTO] Using password-based key derivation")
            except Exception as e:
                logger.error(f"❌ [CRYPTO] Failed to initialize Fernet: {e}")
                # Create a fallback key
                key = Fernet.generate_key()
                self._fernet = Fernet(key)
        
        return self._fernet
    
    def encrypt_value(self, value: str) -> str:
        """Encrypt a string value"""
        try:
            fernet = self._get_fernet()
            encrypted_bytes = fernet.encrypt(value.encode())
            return base64.urlsafe_b64encode(encrypted_bytes).decode()
        except Exception as e:
            logger.error(f"❌ [CRYPTO] Encryption failed: {e}")
            return value  # Return original value if encryption fails
    
    def decrypt_value(self, encrypted_value: str) -> str:
        """Decrypt an encrypted string value, handling double-encryption"""
        try:
            # Check if the value is actually encrypted (starts with gAAAAA)
            if not encrypted_value.startswith('gAAAAA'):
                logger.debug(f"🔐 [CRYPTO] Value doesn't appear encrypted, returning as-is")
                return encrypted_value

            fernet = self._get_fernet()

            # First decryption attempt
            try:
                encrypted_bytes = base64.urlsafe_b64decode(encrypted_value.encode())
                decrypted_bytes = fernet.decrypt(encrypted_bytes)
                first_decrypt = decrypted_bytes.decode()

                # Check if the result is still encrypted (double-encryption)
                if first_decrypt.startswith('gAAAAA'):
                    logger.info("🔐 [CRYPTO] Detected double-encryption, performing second decryption")
                    # Second decryption for double-encrypted values
                    encrypted_bytes_2 = base64.urlsafe_b64decode(first_decrypt.encode())
                    decrypted_bytes_2 = fernet.decrypt(encrypted_bytes_2)
                    final_result = decrypted_bytes_2.decode()
                    logger.info("✅ [CRYPTO] Double-encryption successfully resolved")
                    return final_result
                else:
                    logger.info("✅ [CRYPTO] Single decryption successful")
                    return first_decrypt

            except Exception as decrypt_error:
                logger.warning(f"⚠️ [CRYPTO] Decryption failed: {decrypt_error}")
                # Return None to indicate decryption failure instead of encrypted value
                return None

        except Exception as e:
            logger.error(f"❌ [CRYPTO] Critical decryption error: {e}")
            return None  # Return None to indicate decryption failure

# Global instance for easy access
_secure_credentials = SecureCredentials()

def encrypt_value(value: str) -> str:
    """Encrypt a string value using global instance"""
    return _secure_credentials.encrypt_value(value)

def decrypt_value(encrypted_value: str) -> str:
    """Decrypt an encrypted string value using global instance"""
    return _secure_credentials.decrypt_value(encrypted_value)

def get_secure_credential(key: str, default: Optional[str] = None) -> Optional[str]:
    """Get and decrypt a credential from environment variables"""
    encrypted_value = os.getenv(key, default)
    if encrypted_value:
        return decrypt_value(encrypted_value)
    return None

# Backward compatibility functions
def load_encrypted_credential(key: str) -> Optional[str]:
    """Load and decrypt credential from environment"""
    return get_secure_credential(key)

def save_encrypted_credential(key: str, value: str) -> bool:
    """Encrypt and save credential (for setup scripts)"""
    try:
        encrypted_value = encrypt_value(value)
        # In a real implementation, this would save to a secure store
        logger.info(f"✅ [CRYPTO] Credential {key} encrypted successfully")
        logger.info(f"Set environment variable: {key}={encrypted_value}")
        return True
    except Exception as e:
        logger.error(f"❌ [CRYPTO] Failed to save credential {key}: {e}")
        return False
