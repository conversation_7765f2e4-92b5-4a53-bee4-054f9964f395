"""
Market Data Integrity Validator - Ensures all technical indicators and neural inputs derive from live data
"""

import time
import logging
import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Union, Tuple
from decimal import Decimal
import json
import hashlib
import numpy as np

logger = logging.getLogger(__name__)

class MarketDataIntegrityValidator:
    """
    Validates that all technical indicators and neural network inputs derive from live market data.
    Prohibits sample/historical replay data and implements comprehensive data source verification.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.validation_enabled = True
        self.fail_fast_mode = True
        self.max_data_age_seconds = 30  # Market data must be fresher than 30 seconds
        self.min_data_points = 5  # Minimum data points for technical indicators
        
        # Track validation statistics
        self.data_validations = 0
        self.validation_failures = 0
        self.data_source_registry = {}  # Track data sources
        
        # Prohibited data source patterns
        self.prohibited_sources = {
            'sample_data': ['sample', 'demo', 'test', 'mock', 'fake', 'synthetic'],
            'historical_replay': ['replay', 'backtest', 'historical', 'cached', 'offline'],
            'generated_data': ['generated', 'simulated', 'artificial', 'random', 'dummy']
        }
        
        # Technical indicator validation rules
        self.indicator_validators = {
            'sma': self._validate_sma,
            'ema': self._validate_ema,
            'rsi': self._validate_rsi,
            'macd': self._validate_macd,
            'bollinger_bands': self._validate_bollinger_bands,
            'volume_profile': self._validate_volume_profile,
            'price_action': self._validate_price_action
        }
        
        # Neural input validation rules
        self.neural_validators = {
            'price_features': self._validate_price_features,
            'volume_features': self._validate_volume_features,
            'technical_features': self._validate_technical_features,
            'sentiment_features': self._validate_sentiment_features,
            'market_microstructure': self._validate_microstructure_features
        }
        
        logger.info("🔒 [MARKET-DATA-INTEGRITY] Initialized with live-data-only validation")
    
    async def validate_technical_indicator_data(self, indicator_data: Dict[str, Any], 
                                              indicator_type: str, symbol: str) -> Tuple[bool, Dict[str, Any]]:
        """
        Validate technical indicator data derives from live market data
        
        Returns:
            Tuple[bool, Dict[str, Any]]: (is_valid, validation_details)
        """
        validation_details = {
            'indicator_type': indicator_type,
            'symbol': symbol,
            'validation_timestamp': time.time(),
            'checks_performed': [],
            'warnings': [],
            'errors': [],
            'data_summary': {}
        }
        
        try:
            self.data_validations += 1
            
            # 1. Validate data freshness
            if not self._validate_data_freshness(indicator_data, validation_details):
                return False, validation_details
            
            # 2. Validate data source authenticity
            if not self._validate_data_source_authenticity(indicator_data, validation_details):
                return False, validation_details
            
            # 3. Validate data structure and completeness
            if not self._validate_data_structure(indicator_data, validation_details):
                return False, validation_details
            
            # 4. Indicator-specific validation
            if not await self._perform_indicator_specific_validation(
                indicator_data, indicator_type, validation_details):
                return False, validation_details
            
            # 5. Validate data consistency
            if not self._validate_data_consistency(indicator_data, validation_details):
                return False, validation_details
            
            # 6. Validate against prohibited patterns
            if not self._validate_no_prohibited_patterns(indicator_data, validation_details):
                return False, validation_details
            
            validation_details['status'] = 'VALID'
            logger.debug(f"✅ [MARKET-DATA-INTEGRITY] {indicator_type} indicator validated for {symbol}")
            return True, validation_details
            
        except Exception as e:
            validation_details['errors'].append(f"Validation exception: {str(e)}")
            validation_details['status'] = 'ERROR'
            self.validation_failures += 1
            
            if self.fail_fast_mode:
                logger.error(f"❌ [MARKET-DATA-INTEGRITY] Indicator validation failed: {e}")
                raise RuntimeError(f"CRITICAL: Market data integrity validation failed - {e}")
            else:
                logger.warning(f"⚠️ [MARKET-DATA-INTEGRITY] Indicator validation warning: {e}")
                return False, validation_details
    
    async def validate_neural_input_data(self, neural_data: Dict[str, Any], 
                                       model_name: str) -> Tuple[bool, Dict[str, Any]]:
        """
        Validate neural network input data derives from live sources
        
        Returns:
            Tuple[bool, Dict[str, Any]]: (is_valid, validation_details)
        """
        validation_details = {
            'model_name': model_name,
            'validation_timestamp': time.time(),
            'checks_performed': [],
            'warnings': [],
            'errors': [],
            'feature_summary': {}
        }
        
        try:
            # 1. Validate input data freshness
            if not self._validate_data_freshness(neural_data, validation_details):
                return False, validation_details
            
            # 2. Validate feature authenticity
            if not self._validate_feature_authenticity(neural_data, validation_details):
                return False, validation_details
            
            # 3. Validate training data source
            if not self._validate_training_data_source(neural_data, validation_details):
                return False, validation_details
            
            # 4. Feature-specific validation
            if not await self._perform_neural_feature_validation(neural_data, validation_details):
                return False, validation_details
            
            # 5. Validate data lineage
            if not self._validate_data_lineage(neural_data, validation_details):
                return False, validation_details
            
            validation_details['status'] = 'VALID'
            logger.debug(f"✅ [MARKET-DATA-INTEGRITY] Neural input validated for {model_name}")
            return True, validation_details
            
        except Exception as e:
            validation_details['errors'].append(f"Neural validation exception: {str(e)}")
            validation_details['status'] = 'ERROR'
            self.validation_failures += 1
            
            if self.fail_fast_mode:
                logger.error(f"❌ [MARKET-DATA-INTEGRITY] Neural validation failed: {e}")
                raise RuntimeError(f"CRITICAL: Neural data integrity validation failed - {e}")
            else:
                logger.warning(f"⚠️ [MARKET-DATA-INTEGRITY] Neural validation warning: {e}")
                return False, validation_details
    
    def _validate_data_freshness(self, data: Dict[str, Any], validation_details: Dict[str, Any]) -> bool:
        """Validate data is fresh and not stale"""
        validation_details['checks_performed'].append('data_freshness')
        
        # Extract timestamp
        timestamp = self._extract_timestamp(data)
        if not timestamp:
            validation_details['warnings'].append("No timestamp found in data")
            return True  # Allow if no timestamp (some indicators may not have explicit timestamps)
        
        current_time = time.time()
        age_seconds = current_time - timestamp
        
        if age_seconds > self.max_data_age_seconds:
            validation_details['errors'].append(
                f"Data too old: {age_seconds:.1f}s > {self.max_data_age_seconds}s"
            )
            return False
        
        validation_details['data_age_seconds'] = age_seconds
        return True
    
    def _validate_data_source_authenticity(self, data: Dict[str, Any], validation_details: Dict[str, Any]) -> bool:
        """Validate data source is authentic and not prohibited"""
        validation_details['checks_performed'].append('data_source_authenticity')
        
        # Check for data source information
        data_source = data.get('data_source') or data.get('source') or data.get('provider')
        if data_source:
            source_str = str(data_source).lower()
            
            # Check against prohibited sources
            for category, patterns in self.prohibited_sources.items():
                for pattern in patterns:
                    if pattern in source_str:
                        validation_details['errors'].append(
                            f"Prohibited data source detected: {pattern} in {data_source}"
                        )
                        return False
            
            validation_details['data_source'] = data_source
        else:
            validation_details['warnings'].append("No data source information found")
        
        return True
    
    def _validate_data_structure(self, data: Dict[str, Any], validation_details: Dict[str, Any]) -> bool:
        """Validate data structure is complete and valid"""
        validation_details['checks_performed'].append('data_structure')
        
        # Check for required fields
        if 'values' not in data and 'data' not in data and 'prices' not in data:
            validation_details['errors'].append("No data values found")
            return False
        
        # Extract data values
        values = data.get('values') or data.get('data') or data.get('prices')
        if not values:
            validation_details['errors'].append("Empty data values")
            return False
        
        # Check minimum data points
        if isinstance(values, (list, tuple, np.ndarray)):
            if len(values) < self.min_data_points:
                validation_details['errors'].append(
                    f"Insufficient data points: {len(values)} < {self.min_data_points}"
                )
                return False
            validation_details['data_summary']['data_points'] = len(values)
        
        return True
    
    async def _perform_indicator_specific_validation(self, data: Dict[str, Any], 
                                                   indicator_type: str, validation_details: Dict[str, Any]) -> bool:
        """Perform indicator-specific validation"""
        validation_details['checks_performed'].append('indicator_specific')
        
        indicator_type_lower = indicator_type.lower()
        if indicator_type_lower in self.indicator_validators:
            return await self.indicator_validators[indicator_type_lower](data, validation_details)
        else:
            validation_details['warnings'].append(f"No specific validator for indicator: {indicator_type}")
            return True
    
    async def _validate_sma(self, data: Dict[str, Any], validation_details: Dict[str, Any]) -> bool:
        """Validate Simple Moving Average data"""
        values = data.get('values', [])
        if not values:
            return False
        
        # Check for realistic SMA values
        for value in values:
            if isinstance(value, (int, float)):
                if value <= 0:
                    validation_details['warnings'].append(f"Invalid SMA value: {value}")
        
        return True
    
    async def _validate_ema(self, data: Dict[str, Any], validation_details: Dict[str, Any]) -> bool:
        """Validate Exponential Moving Average data"""
        values = data.get('values', [])
        if not values:
            return False
        
        # Check for realistic EMA values and smoothing
        for i, value in enumerate(values[1:], 1):
            if isinstance(value, (int, float)) and isinstance(values[i-1], (int, float)):
                # EMA should show some smoothing characteristics
                change_ratio = abs(value - values[i-1]) / max(values[i-1], 0.001)
                if change_ratio > 0.5:  # More than 50% change is suspicious
                    validation_details['warnings'].append(f"Large EMA change at index {i}: {change_ratio:.2%}")
        
        return True
    
    async def _validate_rsi(self, data: Dict[str, Any], validation_details: Dict[str, Any]) -> bool:
        """Validate RSI data"""
        values = data.get('values', [])
        if not values:
            return False
        
        # RSI should be between 0 and 100
        for value in values:
            if isinstance(value, (int, float)):
                if value < 0 or value > 100:
                    validation_details['errors'].append(f"Invalid RSI value: {value} (should be 0-100)")
                    return False
        
        return True
    
    async def _validate_macd(self, data: Dict[str, Any], validation_details: Dict[str, Any]) -> bool:
        """Validate MACD data"""
        # MACD should have macd line, signal line, and histogram
        required_components = ['macd', 'signal', 'histogram']
        for component in required_components:
            if component not in data:
                validation_details['warnings'].append(f"Missing MACD component: {component}")
        
        return True
    
    async def _validate_bollinger_bands(self, data: Dict[str, Any], validation_details: Dict[str, Any]) -> bool:
        """Validate Bollinger Bands data"""
        # Should have upper, middle, and lower bands
        required_bands = ['upper', 'middle', 'lower']
        for band in required_bands:
            if band not in data:
                validation_details['warnings'].append(f"Missing Bollinger band: {band}")
        
        # Upper should be > middle > lower
        if all(band in data for band in required_bands):
            upper = data['upper']
            middle = data['middle']
            lower = data['lower']
            
            if isinstance(upper, (list, tuple)) and isinstance(middle, (list, tuple)) and isinstance(lower, (list, tuple)):
                for i, (u, m, l) in enumerate(zip(upper, middle, lower)):
                    if not (u >= m >= l):
                        validation_details['warnings'].append(f"Invalid Bollinger band order at index {i}")
        
        return True
    
    async def _validate_volume_profile(self, data: Dict[str, Any], validation_details: Dict[str, Any]) -> bool:
        """Validate volume profile data"""
        if 'volume' not in data:
            validation_details['errors'].append("No volume data found")
            return False
        
        volumes = data['volume']
        if isinstance(volumes, (list, tuple)):
            for volume in volumes:
                if isinstance(volume, (int, float)) and volume < 0:
                    validation_details['errors'].append(f"Negative volume: {volume}")
                    return False
        
        return True
    
    async def _validate_price_action(self, data: Dict[str, Any], validation_details: Dict[str, Any]) -> bool:
        """Validate price action data"""
        required_fields = ['open', 'high', 'low', 'close']
        for field in required_fields:
            if field not in data:
                validation_details['warnings'].append(f"Missing price field: {field}")
        
        # Validate OHLC relationships
        if all(field in data for field in required_fields):
            for i, (o, h, l, c) in enumerate(zip(data['open'], data['high'], data['low'], data['close'])):
                if not (l <= o <= h and l <= c <= h):
                    validation_details['warnings'].append(f"Invalid OHLC relationship at index {i}")
        
        return True
    
    def _validate_data_consistency(self, data: Dict[str, Any], validation_details: Dict[str, Any]) -> bool:
        """Validate data is internally consistent"""
        validation_details['checks_performed'].append('data_consistency')
        
        # Check for NaN or infinite values
        values = data.get('values') or data.get('data') or []
        if isinstance(values, (list, tuple)):
            for i, value in enumerate(values):
                if isinstance(value, float):
                    if np.isnan(value) or np.isinf(value):
                        validation_details['warnings'].append(f"Invalid value at index {i}: {value}")
        
        return True
    
    def _validate_no_prohibited_patterns(self, data: Dict[str, Any], validation_details: Dict[str, Any]) -> bool:
        """Validate data contains no prohibited patterns"""
        validation_details['checks_performed'].append('prohibited_patterns')
        
        # Convert data to string for pattern matching
        data_str = json.dumps(data, default=str).lower()
        
        # Check for prohibited keywords
        all_prohibited = []
        for category, patterns in self.prohibited_sources.items():
            all_prohibited.extend(patterns)
        
        for pattern in all_prohibited:
            if pattern in data_str:
                validation_details['errors'].append(f"Prohibited pattern found: {pattern}")
                return False
        
        return True
    
    def _validate_feature_authenticity(self, neural_data: Dict[str, Any], validation_details: Dict[str, Any]) -> bool:
        """Validate neural features are authentic"""
        validation_details['checks_performed'].append('feature_authenticity')
        
        features = neural_data.get('features', {})
        for feature_name, feature_value in features.items():
            # Check for synthetic feature names
            feature_name_lower = feature_name.lower()
            if any(pattern in feature_name_lower for pattern in ['mock', 'fake', 'test', 'synthetic']):
                validation_details['errors'].append(f"Synthetic feature detected: {feature_name}")
                return False
        
        return True
    
    def _validate_training_data_source(self, neural_data: Dict[str, Any], validation_details: Dict[str, Any]) -> bool:
        """Validate training data comes from real sources"""
        validation_details['checks_performed'].append('training_data_source')
        
        training_data = neural_data.get('training_data')
        if training_data and isinstance(training_data, dict):
            source = training_data.get('source', '').lower()
            if any(pattern in source for pattern in ['synthetic', 'generated', 'simulated']):
                validation_details['errors'].append(f"Synthetic training data source: {source}")
                return False
        
        return True
    
    async def _perform_neural_feature_validation(self, neural_data: Dict[str, Any], 
                                               validation_details: Dict[str, Any]) -> bool:
        """Perform neural feature-specific validation"""
        validation_details['checks_performed'].append('neural_feature_validation')
        
        features = neural_data.get('features', {})
        for feature_type in features.keys():
            feature_type_lower = feature_type.lower()
            if feature_type_lower in self.neural_validators:
                if not await self.neural_validators[feature_type_lower](features[feature_type], validation_details):
                    return False
        
        return True
    
    async def _validate_price_features(self, feature_data: Any, validation_details: Dict[str, Any]) -> bool:
        """Validate price-based features"""
        if isinstance(feature_data, (list, tuple, np.ndarray)):
            for price in feature_data:
                if isinstance(price, (int, float)) and price <= 0:
                    validation_details['warnings'].append(f"Invalid price feature: {price}")
        return True
    
    async def _validate_volume_features(self, feature_data: Any, validation_details: Dict[str, Any]) -> bool:
        """Validate volume-based features"""
        if isinstance(feature_data, (list, tuple, np.ndarray)):
            for volume in feature_data:
                if isinstance(volume, (int, float)) and volume < 0:
                    validation_details['warnings'].append(f"Invalid volume feature: {volume}")
        return True
    
    async def _validate_technical_features(self, feature_data: Any, validation_details: Dict[str, Any]) -> bool:
        """Validate technical indicator features"""
        # Technical features should be within reasonable ranges
        return True
    
    async def _validate_sentiment_features(self, feature_data: Any, validation_details: Dict[str, Any]) -> bool:
        """Validate sentiment-based features"""
        # Sentiment features should be normalized
        if isinstance(feature_data, (list, tuple, np.ndarray)):
            for sentiment in feature_data:
                if isinstance(sentiment, (int, float)):
                    if sentiment < -1 or sentiment > 1:
                        validation_details['warnings'].append(f"Sentiment out of range: {sentiment}")
        return True
    
    async def _validate_microstructure_features(self, feature_data: Any, validation_details: Dict[str, Any]) -> bool:
        """Validate market microstructure features"""
        # Microstructure features should be realistic
        return True
    
    def _validate_data_lineage(self, neural_data: Dict[str, Any], validation_details: Dict[str, Any]) -> bool:
        """Validate data lineage and traceability"""
        validation_details['checks_performed'].append('data_lineage')
        
        # Check for data lineage information
        lineage = neural_data.get('lineage') or neural_data.get('provenance')
        if lineage:
            validation_details['data_lineage'] = lineage
        else:
            validation_details['warnings'].append("No data lineage information found")
        
        return True
    
    def _extract_timestamp(self, data: Dict[str, Any]) -> Optional[float]:
        """Extract timestamp from data"""
        timestamp_fields = ['timestamp', 'time', 'created_at', 'updated_at']
        
        for field in timestamp_fields:
            if field in data:
                timestamp = data[field]
                try:
                    if isinstance(timestamp, (int, float)):
                        if timestamp > 1e12:  # Milliseconds
                            return timestamp / 1000
                        else:  # Seconds
                            return float(timestamp)
                    elif isinstance(timestamp, str):
                        dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                        return dt.timestamp()
                except (ValueError, TypeError):
                    continue
        
        return None
    
    def get_integrity_statistics(self) -> Dict[str, Any]:
        """Get market data integrity statistics"""
        success_rate = ((self.data_validations - self.validation_failures) / max(self.data_validations, 1)) * 100
        
        return {
            'total_validations': self.data_validations,
            'validation_failures': self.validation_failures,
            'success_rate_percent': round(success_rate, 2),
            'validator_status': 'ACTIVE' if self.validation_enabled else 'DISABLED',
            'fail_fast_mode': self.fail_fast_mode,
            'max_data_age_seconds': self.max_data_age_seconds,
            'supported_indicators': list(self.indicator_validators.keys()),
            'supported_neural_features': list(self.neural_validators.keys())
        }
