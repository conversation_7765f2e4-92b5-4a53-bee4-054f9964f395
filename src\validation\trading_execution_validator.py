"""
Trading Execution Validator - Ensures all trades execute against real exchanges with actual money
"""

import time
import logging
import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Union, Tuple
from decimal import Decimal
import json
import re
import hashlib

logger = logging.getLogger(__name__)

class TradingExecutionValidator:
    """
    Validates that all trading operations execute against real exchanges with actual money.
    Implements transaction ID verification and fail-fast mechanisms.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.validation_enabled = True
        self.fail_fast_mode = True
        self.max_execution_age_seconds = 60  # Order executions must be fresher than 1 minute
        
        # Track execution validation
        self.execution_count = 0
        self.validation_failures = 0
        self.execution_history = []
        
        # Real exchange transaction ID patterns
        self.exchange_tx_patterns = {
            'bybit': {
                'order_id_pattern': r'^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$',
                'min_id_length': 20,
                'required_fields': ['orderId', 'symbol', 'side', 'orderType', 'qty'],
                'status_field': 'orderStatus'
            },
            'coinbase': {
                'order_id_pattern': r'^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$',
                'min_id_length': 20,
                'required_fields': ['id', 'product_id', 'side', 'type', 'size'],
                'status_field': 'status'
            },
            'binance': {
                'order_id_pattern': r'^\d{10,}$',
                'min_id_length': 8,
                'required_fields': ['orderId', 'symbol', 'side', 'type', 'origQty'],
                'status_field': 'status'
            }
        }
        
        # Prohibited test/fake transaction patterns
        self.fake_tx_patterns = [
            'mock', 'fake', 'test', 'dummy', 'sample', 'placeholder',
            '12345', 'abcdef', '00000', '11111', 'aaaaa'
        ]
        
        logger.info("🔒 [TRADING-EXECUTION-VALIDATOR] Initialized with real-money-only validation")
    
    async def validate_order_execution(self, order_data: Dict[str, Any], exchange: str, 
                                     execution_timestamp: float) -> Tuple[bool, Dict[str, Any]]:
        """
        Validate order execution is real and from actual exchange
        
        Returns:
            Tuple[bool, Dict[str, Any]]: (is_valid, validation_details)
        """
        validation_details = {
            'exchange': exchange,
            'execution_timestamp': execution_timestamp,
            'validation_timestamp': time.time(),
            'checks_performed': [],
            'warnings': [],
            'errors': [],
            'order_data': order_data
        }
        
        try:
            self.execution_count += 1
            
            # 1. Validate transaction ID authenticity
            if not self._validate_transaction_id(order_data, exchange, validation_details):
                return False, validation_details
            
            # 2. Validate execution timestamp
            if not self._validate_execution_timestamp(order_data, execution_timestamp, validation_details):
                return False, validation_details
            
            # 3. Validate order structure
            if not self._validate_order_structure(order_data, exchange, validation_details):
                return False, validation_details
            
            # 4. Validate order amounts are real
            if not self._validate_order_amounts(order_data, validation_details):
                return False, validation_details
            
            # 5. Validate execution status
            if not self._validate_execution_status(order_data, exchange, validation_details):
                return False, validation_details
            
            # 6. Validate against simulation indicators
            if not self._validate_no_simulation_indicators(order_data, validation_details):
                return False, validation_details
            
            # 7. Validate order consistency
            if not self._validate_order_consistency(order_data, validation_details):
                return False, validation_details
            
            validation_details['status'] = 'VALID'
            logger.info(f"✅ [TRADING-EXECUTION-VALIDATOR] Order execution validated for {exchange}")
            
            # Store execution in history
            self.execution_history.append({
                'timestamp': time.time(),
                'exchange': exchange,
                'order_id': self._extract_order_id(order_data, exchange),
                'validation_result': True
            })
            
            return True, validation_details
            
        except Exception as e:
            validation_details['errors'].append(f"Validation exception: {str(e)}")
            validation_details['status'] = 'ERROR'
            self.validation_failures += 1
            
            if self.fail_fast_mode:
                logger.error(f"❌ [TRADING-EXECUTION-VALIDATOR] Execution validation failed for {exchange}: {e}")
                raise RuntimeError(f"CRITICAL: Trading execution validation failed - {e}")
            else:
                logger.warning(f"⚠️ [TRADING-EXECUTION-VALIDATOR] Execution validation warning for {exchange}: {e}")
                return False, validation_details
    
    def _validate_transaction_id(self, order_data: Dict[str, Any], exchange: str,
                               validation_details: Dict[str, Any]) -> bool:
        """Validate transaction ID is authentic and not fake"""
        validation_details['checks_performed'].append('transaction_id')
        
        order_id = self._extract_order_id(order_data, exchange)
        if not order_id:
            validation_details['errors'].append("No order ID found in execution data")
            return False
        
        order_id_str = str(order_id).lower()
        
        # Check for fake patterns
        for fake_pattern in self.fake_tx_patterns:
            if fake_pattern in order_id_str:
                validation_details['errors'].append(f"Fake transaction ID pattern detected: {fake_pattern}")
                return False
        
        # Check minimum length
        exchange_lower = exchange.lower()
        if exchange_lower in self.exchange_tx_patterns:
            min_length = self.exchange_tx_patterns[exchange_lower]['min_id_length']
            if len(order_id_str) < min_length:
                validation_details['errors'].append(f"Transaction ID too short: {len(order_id_str)} < {min_length}")
                return False
            
            # Check pattern if available
            pattern = self.exchange_tx_patterns[exchange_lower].get('order_id_pattern')
            if pattern and not re.match(pattern, order_id_str):
                validation_details['warnings'].append(f"Transaction ID doesn't match expected pattern for {exchange}")
        
        validation_details['order_id'] = order_id
        return True
    
    def _validate_execution_timestamp(self, order_data: Dict[str, Any], execution_timestamp: float,
                                    validation_details: Dict[str, Any]) -> bool:
        """Validate execution timestamp is recent and realistic"""
        validation_details['checks_performed'].append('execution_timestamp')
        
        current_time = time.time()
        
        # Check execution timestamp from order data
        order_timestamp = self._extract_order_timestamp(order_data)
        if order_timestamp:
            age_seconds = current_time - order_timestamp
            if age_seconds > self.max_execution_age_seconds:
                validation_details['errors'].append(
                    f"Order execution too old: {age_seconds:.1f}s > {self.max_execution_age_seconds}s"
                )
                return False
            
            # Check if timestamp is not in the future
            if order_timestamp > current_time + 60:  # Allow 1 minute clock skew
                validation_details['errors'].append(
                    f"Order timestamp in future: {order_timestamp} > {current_time}"
                )
                return False
        else:
            # Use provided execution timestamp
            age_seconds = current_time - execution_timestamp
            if age_seconds > self.max_execution_age_seconds:
                validation_details['warnings'].append(
                    f"No order timestamp, using execution timestamp age: {age_seconds:.1f}s"
                )
        
        validation_details['order_timestamp'] = order_timestamp
        validation_details['execution_age_seconds'] = current_time - (order_timestamp or execution_timestamp)
        
        return True
    
    def _validate_order_structure(self, order_data: Dict[str, Any], exchange: str,
                                validation_details: Dict[str, Any]) -> bool:
        """Validate order has required fields for the exchange"""
        validation_details['checks_performed'].append('order_structure')
        
        exchange_lower = exchange.lower()
        if exchange_lower in self.exchange_tx_patterns:
            required_fields = self.exchange_tx_patterns[exchange_lower]['required_fields']
            missing_fields = []
            
            for field in required_fields:
                if field not in order_data:
                    missing_fields.append(field)
            
            if missing_fields:
                # Be more flexible - warn instead of failing if some fields are missing
                validation_details['warnings'].append(f"Missing optional fields: {missing_fields}")
                # Only fail if critical fields are missing
                critical_fields = ['orderId', 'id', 'symbol', 'side']
                missing_critical = [f for f in missing_fields if f in critical_fields]
                if missing_critical:
                    validation_details['errors'].append(f"Missing critical fields: {missing_critical}")
                    return False
        
        return True
    
    def _validate_order_amounts(self, order_data: Dict[str, Any], validation_details: Dict[str, Any]) -> bool:
        """Validate order amounts are realistic and not test values"""
        validation_details['checks_performed'].append('order_amounts')
        
        # Common test amounts to reject
        test_amounts = [1.0, 10.0, 100.0, 1000.0, 0.001, 0.01, 0.1]
        
        # Check quantity/size fields
        amount_fields = ['qty', 'size', 'origQty', 'quantity', 'amount']
        for field in amount_fields:
            if field in order_data:
                try:
                    amount = float(order_data[field])
                    if amount <= 0:
                        validation_details['errors'].append(f"Invalid order amount: {amount}")
                        return False
                    
                    # Check for exact test amounts
                    if amount in test_amounts:
                        validation_details['warnings'].append(f"Potentially test amount: {amount}")
                    
                except (ValueError, TypeError):
                    validation_details['errors'].append(f"Non-numeric amount: {order_data[field]}")
                    return False
        
        # Check price fields
        price_fields = ['price', 'stopPrice', 'avgPrice']
        for field in price_fields:
            if field in order_data:
                try:
                    price = float(order_data[field])
                    if price <= 0:
                        validation_details['errors'].append(f"Invalid price: {price}")
                        return False
                except (ValueError, TypeError):
                    validation_details['warnings'].append(f"Non-numeric price: {order_data[field]}")
        
        return True
    
    def _validate_execution_status(self, order_data: Dict[str, Any], exchange: str,
                                 validation_details: Dict[str, Any]) -> bool:
        """Validate order execution status is valid"""
        validation_details['checks_performed'].append('execution_status')
        
        exchange_lower = exchange.lower()
        if exchange_lower in self.exchange_tx_patterns:
            status_field = self.exchange_tx_patterns[exchange_lower]['status_field']
            if status_field in order_data:
                status = str(order_data[status_field]).upper()
                
                # Valid execution statuses
                valid_statuses = [
                    'FILLED', 'PARTIALLY_FILLED', 'NEW', 'PENDING', 'ACTIVE',
                    'OPEN', 'CLOSED', 'CANCELLED', 'REJECTED', 'EXPIRED'
                ]
                
                if status not in valid_statuses:
                    validation_details['warnings'].append(f"Unknown order status: {status}")
                
                validation_details['order_status'] = status
        
        return True
    
    def _validate_no_simulation_indicators(self, order_data: Dict[str, Any],
                                         validation_details: Dict[str, Any]) -> bool:
        """Validate order contains no simulation/test indicators"""
        validation_details['checks_performed'].append('simulation_indicators')
        
        # Convert order data to string for pattern matching
        order_str = json.dumps(order_data, default=str).lower()
        
        # Check for simulation keywords
        simulation_keywords = ['test', 'mock', 'fake', 'simulation', 'demo', 'sandbox', 'paper']
        for keyword in simulation_keywords:
            if keyword in order_str:
                validation_details['errors'].append(f"Simulation indicator found: {keyword}")
                return False
        
        return True
    
    def _validate_order_consistency(self, order_data: Dict[str, Any],
                                  validation_details: Dict[str, Any]) -> bool:
        """Validate order data is internally consistent"""
        validation_details['checks_performed'].append('order_consistency')
        
        # Check side consistency
        side = order_data.get('side', '').upper()
        if side not in ['BUY', 'SELL', 'BID', 'ASK']:
            validation_details['warnings'].append(f"Unknown order side: {side}")
        
        # Check order type consistency
        order_type = order_data.get('type', order_data.get('orderType', '')).upper()
        valid_types = ['MARKET', 'LIMIT', 'STOP', 'STOP_LIMIT', 'TAKE_PROFIT']
        if order_type and order_type not in valid_types:
            validation_details['warnings'].append(f"Unknown order type: {order_type}")
        
        return True
    
    def _extract_order_id(self, order_data: Dict[str, Any], exchange: str) -> Optional[str]:
        """Extract order ID from order data"""
        id_fields = ['orderId', 'id', 'order_id', 'clientOrderId', 'client_order_id']
        
        for field in id_fields:
            if field in order_data:
                return str(order_data[field])
        
        return None
    
    def _extract_order_timestamp(self, order_data: Dict[str, Any]) -> Optional[float]:
        """Extract timestamp from order data"""
        timestamp_fields = ['timestamp', 'time', 'transactTime', 'created_at', 'updated_at']
        
        for field in timestamp_fields:
            if field in order_data:
                timestamp = order_data[field]
                try:
                    if isinstance(timestamp, (int, float)):
                        # Handle both seconds and milliseconds timestamps
                        if timestamp > 1e12:  # Milliseconds
                            return timestamp / 1000
                        else:  # Seconds
                            return float(timestamp)
                    elif isinstance(timestamp, str):
                        # Try parsing ISO format
                        dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                        return dt.timestamp()
                except (ValueError, TypeError):
                    continue
        
        return None
    
    async def validate_balance_update(self, balance_data: Dict[str, Any], exchange: str) -> bool:
        """Validate balance update is from real trading execution"""
        try:
            # Check timestamp freshness
            timestamp = self._extract_order_timestamp(balance_data)
            if timestamp:
                age_seconds = time.time() - timestamp
                if age_seconds > self.max_execution_age_seconds:
                    raise ValueError(f"Balance update too old: {age_seconds:.1f}s")
            
            # Validate balance amounts are realistic
            for currency, amount in balance_data.get('balances', {}).items():
                if isinstance(amount, (int, float, Decimal)):
                    if float(amount) < 0:
                        raise ValueError(f"Negative balance detected: {currency}={amount}")
            
            logger.debug(f"✅ [TRADING-EXECUTION-VALIDATOR] Balance update validated for {exchange}")
            return True
            
        except Exception as e:
            if self.fail_fast_mode:
                logger.error(f"❌ [TRADING-EXECUTION-VALIDATOR] Balance validation failed: {e}")
                raise RuntimeError(f"CRITICAL: Balance update validation failed - {e}")
            else:
                logger.warning(f"⚠️ [TRADING-EXECUTION-VALIDATOR] Balance validation warning: {e}")
                return False
    
    def get_execution_statistics(self) -> Dict[str, Any]:
        """Get execution validation statistics"""
        success_rate = ((self.execution_count - self.validation_failures) / max(self.execution_count, 1)) * 100
        
        return {
            'total_executions': self.execution_count,
            'validation_failures': self.validation_failures,
            'success_rate_percent': round(success_rate, 2),
            'validator_status': 'ACTIVE' if self.validation_enabled else 'DISABLED',
            'fail_fast_mode': self.fail_fast_mode,
            'max_execution_age_seconds': self.max_execution_age_seconds,
            'recent_executions': len([e for e in self.execution_history if time.time() - e['timestamp'] < 3600])
        }
