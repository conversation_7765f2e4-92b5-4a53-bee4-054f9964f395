"""
Real Data Stream Manager - Continuous live data stream management and restoration
"""

import time
import logging
import asyncio
import websockets
import json
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Union, Callable, Set
from decimal import Decimal
import threading
from concurrent.futures import ThreadPoolExecutor
import aiohttp

logger = logging.getLogger(__name__)

class RealDataStreamManager:
    """
    Manages continuous live data streams from exchanges.
    Automatically maintains connections, detects failures, and restores streams.
    Ensures uninterrupted flow of real market data for trading operations.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.stream_manager_enabled = True
        self.auto_recovery_enabled = True
        
        # Stream configuration
        self.max_reconnection_attempts = 5
        self.reconnection_delay = 5
        self.heartbeat_interval = 30
        self.stream_timeout = 60
        self.data_freshness_threshold = 30
        
        # Active streams tracking
        self.active_streams = {}
        self.stream_connections = {}
        self.stream_health_status = {}
        self.stream_subscriptions = {}
        
        # Data buffers
        self.live_data_buffer = {}
        self.stream_metadata = {}
        self.last_data_timestamps = {}
        
        # Recovery and monitoring
        self.failed_streams = set()
        self.recovery_queue = asyncio.Queue()
        self.monitoring_tasks = {}
        
        # Statistics
        self.stream_statistics = {
            'total_streams': 0,
            'active_streams': 0,
            'failed_streams': 0,
            'recovered_streams': 0,
            'data_messages_received': 0,
            'connection_failures': 0,
            'successful_recoveries': 0
        }
        
        # Exchange endpoints
        self.exchange_endpoints = {
            'bybit': {
                'websocket': 'wss://stream.bybit.com/v5/public/linear',
                'rest_api': 'https://api.bybit.com/v5',
                'testnet_ws': 'wss://stream-testnet.bybit.com/v5/public/linear',
                'testnet_api': 'https://api-testnet.bybit.com/v5'
            },
            'coinbase': {
                'websocket': 'wss://ws-feed.exchange.coinbase.com',
                'rest_api': 'https://api.exchange.coinbase.com',
                'sandbox_ws': 'wss://ws-feed-public.sandbox.exchange.coinbase.com',
                'sandbox_api': 'https://api-public.sandbox.exchange.coinbase.com'
            }
        }
        
        # Thread pool for stream management
        self.stream_executor = ThreadPoolExecutor(max_workers=10, thread_name_prefix="StreamManager")
        
        logger.info("🌊 [STREAM-MANAGER] Initialized real data stream manager")
    
    async def start_live_data_streams(self, required_streams: List[Dict[str, Any]]) -> bool:
        """
        Start all required live data streams
        
        Args:
            required_streams: List of stream configurations
            
        Returns:
            bool: True if all streams started successfully
        """
        try:
            logger.info(f"🚀 [STREAM-MANAGER] Starting {len(required_streams)} live data streams")
            
            # Start each required stream
            stream_results = []
            for stream_config in required_streams:
                result = await self._start_individual_stream(stream_config)
                stream_results.append(result)
            
            # Check overall success
            successful_streams = sum(stream_results)
            total_streams = len(required_streams)
            
            self.stream_statistics['total_streams'] = total_streams
            self.stream_statistics['active_streams'] = successful_streams
            
            if successful_streams == total_streams:
                logger.info(f"✅ [STREAM-MANAGER] All {total_streams} streams started successfully")
                
                # Start monitoring
                await self._start_stream_monitoring()
                
                return True
            else:
                logger.warning(f"⚠️ [STREAM-MANAGER] Only {successful_streams}/{total_streams} streams started successfully")
                
                # Start recovery for failed streams
                await self._start_recovery_process()
                
                return successful_streams > 0
                
        except Exception as e:
            logger.error(f"❌ [STREAM-MANAGER] Failed to start live data streams: {e}")
            return False
    
    async def _start_individual_stream(self, stream_config: Dict[str, Any]) -> bool:
        """Start an individual data stream"""
        try:
            stream_id = stream_config['stream_id']
            exchange = stream_config['exchange']
            data_type = stream_config['data_type']
            
            logger.info(f"🔗 [STREAM-MANAGER] Starting stream: {stream_id} ({exchange}/{data_type})")
            
            # Validate exchange endpoint
            if exchange not in self.exchange_endpoints:
                raise ValueError(f"Unsupported exchange: {exchange}")
            
            # Ensure we're using live endpoints (not testnet/sandbox)
            endpoint_config = self.exchange_endpoints[exchange]
            if self._is_test_endpoint(endpoint_config):
                raise ValueError(f"Test endpoint detected for {exchange} - only live endpoints allowed")
            
            # Start stream based on type
            if data_type == 'websocket':
                success = await self._start_websocket_stream(stream_id, stream_config)
            elif data_type == 'rest_polling':
                success = await self._start_rest_polling_stream(stream_id, stream_config)
            else:
                raise ValueError(f"Unsupported data type: {data_type}")
            
            if success:
                # Initialize stream metadata
                self.stream_metadata[stream_id] = {
                    'config': stream_config,
                    'started_at': time.time(),
                    'last_data_time': time.time(),
                    'message_count': 0,
                    'status': 'ACTIVE'
                }
                
                self.stream_health_status[stream_id] = 'HEALTHY'
                self.last_data_timestamps[stream_id] = time.time()
                
                logger.info(f"✅ [STREAM-MANAGER] Stream started successfully: {stream_id}")
                return True
            else:
                logger.error(f"❌ [STREAM-MANAGER] Failed to start stream: {stream_id}")
                self.failed_streams.add(stream_id)
                return False
                
        except Exception as e:
            logger.error(f"❌ [STREAM-MANAGER] Error starting stream {stream_config.get('stream_id', 'unknown')}: {e}")
            return False
    
    async def _start_websocket_stream(self, stream_id: str, config: Dict[str, Any]) -> bool:
        """Start a WebSocket data stream"""
        try:
            exchange = config['exchange']
            endpoint = self.exchange_endpoints[exchange]['websocket']
            
            # Create WebSocket connection
            websocket = await websockets.connect(endpoint)
            self.stream_connections[stream_id] = websocket
            
            # Subscribe to required channels
            subscription_message = self._build_subscription_message(config)
            if subscription_message:
                await websocket.send(json.dumps(subscription_message))
            
            # Start message handling task
            task = asyncio.create_task(self._handle_websocket_messages(stream_id, websocket))
            self.monitoring_tasks[stream_id] = task
            
            logger.info(f"🌐 [STREAM-MANAGER] WebSocket stream connected: {stream_id} -> {endpoint}")
            return True
            
        except Exception as e:
            logger.error(f"❌ [STREAM-MANAGER] WebSocket connection failed for {stream_id}: {e}")
            return False
    
    async def _start_rest_polling_stream(self, stream_id: str, config: Dict[str, Any]) -> bool:
        """Start a REST API polling stream"""
        try:
            # Start polling task
            task = asyncio.create_task(self._poll_rest_endpoint(stream_id, config))
            self.monitoring_tasks[stream_id] = task
            
            logger.info(f"🔄 [STREAM-MANAGER] REST polling stream started: {stream_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ [STREAM-MANAGER] REST polling failed for {stream_id}: {e}")
            return False
    
    async def _handle_websocket_messages(self, stream_id: str, websocket):
        """Handle incoming WebSocket messages"""
        try:
            while self.stream_manager_enabled:
                try:
                    # Receive message with timeout
                    message = await asyncio.wait_for(websocket.recv(), timeout=self.stream_timeout)
                    
                    # Process message
                    await self._process_stream_message(stream_id, message, 'websocket')
                    
                except asyncio.TimeoutError:
                    logger.warning(f"⚠️ [STREAM-MANAGER] WebSocket timeout for {stream_id}")
                    await self._handle_stream_timeout(stream_id)
                    break
                    
                except websockets.exceptions.ConnectionClosed:
                    logger.warning(f"⚠️ [STREAM-MANAGER] WebSocket connection closed for {stream_id}")
                    await self._handle_connection_failure(stream_id)
                    break
                    
        except Exception as e:
            logger.error(f"❌ [STREAM-MANAGER] WebSocket message handling error for {stream_id}: {e}")
            await self._handle_stream_error(stream_id, e)
    
    async def _poll_rest_endpoint(self, stream_id: str, config: Dict[str, Any]):
        """Poll REST endpoint for data"""
        try:
            exchange = config['exchange']
            endpoint = config.get('endpoint', '')
            poll_interval = config.get('poll_interval', 10)
            
            base_url = self.exchange_endpoints[exchange]['rest_api']
            full_url = f"{base_url}{endpoint}"
            
            async with aiohttp.ClientSession() as session:
                while self.stream_manager_enabled:
                    try:
                        async with session.get(full_url, timeout=aiohttp.ClientTimeout(total=10)) as response:
                            if response.status == 200:
                                data = await response.json()
                                await self._process_stream_message(stream_id, json.dumps(data), 'rest')
                            else:
                                logger.warning(f"⚠️ [STREAM-MANAGER] REST API error for {stream_id}: {response.status}")
                        
                        await asyncio.sleep(poll_interval)
                        
                    except asyncio.TimeoutError:
                        logger.warning(f"⚠️ [STREAM-MANAGER] REST API timeout for {stream_id}")
                        await self._handle_stream_timeout(stream_id)
                        
                    except Exception as e:
                        logger.error(f"❌ [STREAM-MANAGER] REST polling error for {stream_id}: {e}")
                        await asyncio.sleep(poll_interval)
                        
        except Exception as e:
            logger.error(f"❌ [STREAM-MANAGER] REST polling setup error for {stream_id}: {e}")
            await self._handle_stream_error(stream_id, e)
    
    async def _process_stream_message(self, stream_id: str, message: str, message_type: str):
        """Process incoming stream message"""
        try:
            # Parse message
            if isinstance(message, str):
                data = json.loads(message)
            else:
                data = message
            
            # Add stream metadata
            processed_data = {
                'stream_id': stream_id,
                'message_type': message_type,
                'received_at': time.time(),
                'data': data,
                'data_source': 'live_stream'
            }
            
            # Validate data authenticity
            if self._validate_stream_data(processed_data):
                # Store in live data buffer
                self.live_data_buffer[stream_id] = processed_data
                self.last_data_timestamps[stream_id] = time.time()
                
                # Update statistics
                self.stream_statistics['data_messages_received'] += 1
                
                # Update stream metadata
                if stream_id in self.stream_metadata:
                    self.stream_metadata[stream_id]['last_data_time'] = time.time()
                    self.stream_metadata[stream_id]['message_count'] += 1
                
                # Mark stream as healthy
                self.stream_health_status[stream_id] = 'HEALTHY'
                
                logger.debug(f"📨 [STREAM-MANAGER] Processed message for {stream_id}")
            else:
                logger.warning(f"⚠️ [STREAM-MANAGER] Invalid data received for {stream_id}")
                
        except Exception as e:
            logger.error(f"❌ [STREAM-MANAGER] Message processing error for {stream_id}: {e}")
    
    def _validate_stream_data(self, data: Dict[str, Any]) -> bool:
        """Validate that stream data is authentic and live"""
        try:
            # Check basic structure
            if not isinstance(data, dict):
                return False
            
            # Check for required fields
            if 'received_at' not in data or 'data_source' not in data:
                return False
            
            # Check data source
            if data['data_source'] != 'live_stream':
                return False
            
            # Check timestamp freshness
            received_at = data['received_at']
            age = time.time() - received_at
            if age > self.data_freshness_threshold:
                return False
            
            # Check for prohibited patterns
            data_str = json.dumps(data, default=str).lower()
            prohibited_patterns = ['mock', 'fake', 'test', 'simulation', 'demo']
            
            for pattern in prohibited_patterns:
                if pattern in data_str:
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"❌ [STREAM-MANAGER] Data validation error: {e}")
            return False
    
    def _build_subscription_message(self, config: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Build WebSocket subscription message"""
        exchange = config['exchange']
        
        if exchange == 'bybit':
            return {
                "op": "subscribe",
                "args": config.get('channels', ["tickers.BTCUSDT"])
            }
        elif exchange == 'coinbase':
            return {
                "type": "subscribe",
                "channels": config.get('channels', ["ticker"]),
                "product_ids": config.get('symbols', ["BTC-USD"])
            }
        
        return None
    
    def _is_test_endpoint(self, endpoint_config: Dict[str, Any]) -> bool:
        """Check if endpoint configuration uses test/sandbox endpoints"""
        websocket_url = endpoint_config.get('websocket', '')
        rest_url = endpoint_config.get('rest_api', '')
        
        test_indicators = ['testnet', 'sandbox', 'test', 'demo']
        
        for indicator in test_indicators:
            if indicator in websocket_url.lower() or indicator in rest_url.lower():
                return True
        
        return False
    
    async def _handle_stream_timeout(self, stream_id: str):
        """Handle stream timeout"""
        logger.warning(f"⏰ [STREAM-MANAGER] Stream timeout: {stream_id}")
        
        self.stream_health_status[stream_id] = 'TIMEOUT'
        self.failed_streams.add(stream_id)
        
        # Queue for recovery
        await self.recovery_queue.put({
            'stream_id': stream_id,
            'failure_type': 'timeout',
            'timestamp': time.time()
        })
    
    async def _handle_connection_failure(self, stream_id: str):
        """Handle connection failure"""
        logger.warning(f"🔌 [STREAM-MANAGER] Connection failure: {stream_id}")
        
        self.stream_health_status[stream_id] = 'DISCONNECTED'
        self.failed_streams.add(stream_id)
        self.stream_statistics['connection_failures'] += 1
        
        # Queue for recovery
        await self.recovery_queue.put({
            'stream_id': stream_id,
            'failure_type': 'connection_failure',
            'timestamp': time.time()
        })
    
    async def _handle_stream_error(self, stream_id: str, error: Exception):
        """Handle stream error"""
        logger.error(f"❌ [STREAM-MANAGER] Stream error: {stream_id} - {error}")
        
        self.stream_health_status[stream_id] = 'ERROR'
        self.failed_streams.add(stream_id)
        
        # Queue for recovery
        await self.recovery_queue.put({
            'stream_id': stream_id,
            'failure_type': 'error',
            'error': str(error),
            'timestamp': time.time()
        })
    
    async def _start_stream_monitoring(self):
        """Start continuous stream health monitoring"""
        if not self.stream_manager_enabled:
            return
        
        logger.info("👁️ [STREAM-MANAGER] Starting stream health monitoring")
        
        # Start monitoring task
        monitoring_task = asyncio.create_task(self._monitor_stream_health())
        self.monitoring_tasks['health_monitor'] = monitoring_task
    
    async def _monitor_stream_health(self):
        """Monitor health of all active streams"""
        while self.stream_manager_enabled:
            try:
                current_time = time.time()
                
                for stream_id, last_data_time in self.last_data_timestamps.items():
                    # Check if stream is stale
                    age = current_time - last_data_time
                    
                    if age > self.data_freshness_threshold:
                        if self.stream_health_status.get(stream_id) == 'HEALTHY':
                            logger.warning(f"⚠️ [STREAM-MANAGER] Stream becoming stale: {stream_id} (age: {age:.1f}s)")
                            self.stream_health_status[stream_id] = 'STALE'
                    
                    if age > self.stream_timeout:
                        if stream_id not in self.failed_streams:
                            logger.error(f"❌ [STREAM-MANAGER] Stream failed health check: {stream_id}")
                            await self._handle_stream_timeout(stream_id)
                
                await asyncio.sleep(self.heartbeat_interval)
                
            except Exception as e:
                logger.error(f"❌ [STREAM-MANAGER] Health monitoring error: {e}")
                await asyncio.sleep(5)
    
    async def _start_recovery_process(self):
        """Start automatic stream recovery process"""
        if not self.auto_recovery_enabled:
            return
        
        logger.info("🔧 [STREAM-MANAGER] Starting automatic stream recovery")
        
        # Start recovery task
        recovery_task = asyncio.create_task(self._process_recovery_queue())
        self.monitoring_tasks['recovery_processor'] = recovery_task
    
    async def _process_recovery_queue(self):
        """Process stream recovery queue"""
        while self.stream_manager_enabled:
            try:
                # Wait for recovery request
                recovery_request = await self.recovery_queue.get()
                
                # Attempt recovery
                success = await self._recover_failed_stream(recovery_request)
                
                if success:
                    self.stream_statistics['successful_recoveries'] += 1
                    logger.info(f"✅ [STREAM-MANAGER] Stream recovered: {recovery_request['stream_id']}")
                else:
                    logger.error(f"❌ [STREAM-MANAGER] Stream recovery failed: {recovery_request['stream_id']}")
                
            except Exception as e:
                logger.error(f"❌ [STREAM-MANAGER] Recovery processing error: {e}")
                await asyncio.sleep(5)
    
    async def _recover_failed_stream(self, recovery_request: Dict[str, Any]) -> bool:
        """Recover a failed stream"""
        try:
            stream_id = recovery_request['stream_id']
            
            logger.info(f"🔧 [STREAM-MANAGER] Attempting to recover stream: {stream_id}")
            
            # Get original stream configuration
            if stream_id not in self.stream_metadata:
                logger.error(f"❌ [STREAM-MANAGER] No metadata found for stream: {stream_id}")
                return False
            
            original_config = self.stream_metadata[stream_id]['config']
            
            # Clean up failed stream
            await self._cleanup_failed_stream(stream_id)
            
            # Wait before reconnection
            await asyncio.sleep(self.reconnection_delay)
            
            # Attempt to restart stream
            success = await self._start_individual_stream(original_config)
            
            if success:
                # Remove from failed streams
                self.failed_streams.discard(stream_id)
                self.stream_statistics['recovered_streams'] += 1
                
                logger.info(f"✅ [STREAM-MANAGER] Successfully recovered stream: {stream_id}")
                return True
            else:
                logger.error(f"❌ [STREAM-MANAGER] Failed to recover stream: {stream_id}")
                return False
                
        except Exception as e:
            logger.error(f"❌ [STREAM-MANAGER] Stream recovery error: {e}")
            return False
    
    async def _cleanup_failed_stream(self, stream_id: str):
        """Clean up resources for failed stream"""
        try:
            # Close WebSocket connection if exists
            if stream_id in self.stream_connections:
                connection = self.stream_connections[stream_id]
                if hasattr(connection, 'close'):
                    await connection.close()
                del self.stream_connections[stream_id]
            
            # Cancel monitoring task if exists
            if stream_id in self.monitoring_tasks:
                task = self.monitoring_tasks[stream_id]
                if not task.done():
                    task.cancel()
                del self.monitoring_tasks[stream_id]
            
            # Clear stream data
            self.live_data_buffer.pop(stream_id, None)
            
            logger.debug(f"🧹 [STREAM-MANAGER] Cleaned up failed stream: {stream_id}")
            
        except Exception as e:
            logger.error(f"❌ [STREAM-MANAGER] Stream cleanup error for {stream_id}: {e}")
    
    def get_live_data(self, stream_id: str) -> Optional[Dict[str, Any]]:
        """Get latest live data from stream"""
        return self.live_data_buffer.get(stream_id)
    
    def get_all_live_data(self) -> Dict[str, Any]:
        """Get all current live data"""
        return self.live_data_buffer.copy()
    
    def get_stream_health(self) -> Dict[str, str]:
        """Get health status of all streams"""
        return self.stream_health_status.copy()
    
    def get_stream_statistics(self) -> Dict[str, Any]:
        """Get comprehensive stream statistics"""
        current_time = time.time()
        
        # Update active stream count
        active_count = len([s for s in self.stream_health_status.values() if s == 'HEALTHY'])
        self.stream_statistics['active_streams'] = active_count
        self.stream_statistics['failed_streams'] = len(self.failed_streams)
        
        return {
            **self.stream_statistics,
            'stream_health_summary': {
                'healthy': len([s for s in self.stream_health_status.values() if s == 'HEALTHY']),
                'stale': len([s for s in self.stream_health_status.values() if s == 'STALE']),
                'disconnected': len([s for s in self.stream_health_status.values() if s == 'DISCONNECTED']),
                'error': len([s for s in self.stream_health_status.values() if s == 'ERROR']),
                'timeout': len([s for s in self.stream_health_status.values() if s == 'TIMEOUT'])
            },
            'uptime_seconds': current_time - min([m.get('started_at', current_time) for m in self.stream_metadata.values()], default=current_time)
        }
    
    async def shutdown(self):
        """Gracefully shutdown stream manager"""
        logger.info("🛑 [STREAM-MANAGER] Shutting down stream manager")
        
        self.stream_manager_enabled = False
        
        # Cancel all monitoring tasks
        for task in self.monitoring_tasks.values():
            if not task.done():
                task.cancel()
        
        # Close all connections
        for stream_id in list(self.stream_connections.keys()):
            await self._cleanup_failed_stream(stream_id)
        
        # Shutdown thread pool
        self.stream_executor.shutdown(wait=True)
        
        logger.info("✅ [STREAM-MANAGER] Stream manager shutdown complete")
