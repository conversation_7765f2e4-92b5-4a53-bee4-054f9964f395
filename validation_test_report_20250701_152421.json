{"test_summary": {"total_tests": 10, "passed_tests": 4, "failed_tests": 6, "success_rate_percent": 40.0}, "test_results": {"API Data Validation": {"passed": true, "details": "API validation working correctly"}, "Balance Verification": {"passed": false, "error": "Valid balance failed verification"}, "Market Data Integrity": {"passed": true, "details": "Market data integrity validation working correctly"}, "Trading Execution Validation": {"passed": false, "error": "Valid order execution failed validation"}, "Neural Learning Data Validation": {"passed": true, "details": "Neural learning validation working correctly"}, "Fail-Fast Error Handling": {"passed": false, "error": "CRITICAL: Placeholder value detected: prices=50000.0 in test_price"}, "Runtime Validation Checkpoints": {"passed": true, "details": "Runtime checkpoints working correctly"}, "Master Validator Integration": {"passed": false, "error": "System-wide validation failed: {'validation_timestamp': 1751376261.7725446, 'total_data_sources': 3, 'validation_results': {'api_responses': {'valid': True, 'details': {'pipeline': 'api_response', 'stages': {'bybit_api': {'valid': True, 'details': {'exchange': 'bybit', 'endpoint': 'live_endpoint', 'request_timestamp': 1751376261.7725604, 'validation_timestamp': 1751376261.772562, 'checks_performed': ['response_structure', 'timestamp_freshness', 'endpoint_authenticity', 'exchange_specific', 'data_authenticity', 'response_consistency'], 'warnings': ['Bybit response structure not recognized'], 'errors': [], 'response_timestamp': 1751376261.772524, 'response_age_seconds': 4.76837158203125e-05, 'status': 'VALID'}}, 'bybit_real_data': {'valid': True}}, 'overall_valid': True}}, 'balance_data': {'valid': False, 'details': {'pipeline': 'balance_data', 'stages': {'bybit_balance': {'valid': False, 'details': {'exchange': 'bybit', 'fetch_timestamp': 1751376261.7727332, 'verification_timestamp': 1751376261.7727344, 'checks_performed': ['balance_freshness', 'balance_structure'], 'warnings': [], 'errors': ['No balances extracted from data'], 'balance_summary': {}, 'balance_timestamp': 1751376261.7725272, 'fetch_age_seconds': 3.814697265625e-06}}, 'bybit_real_balance': {'valid': True}}, 'overall_valid': False}}, 'market_data': {'valid': True, 'details': {'pipeline': 'market_data', 'stages': {'BTCUSDT_market': {'valid': True}}, 'overall_valid': True}}}, 'overall_status': 'VALIDATION_FAILED', 'critical_failures': [{'source_type': 'balance_data', 'details': {'pipeline': 'balance_data', 'stages': {'bybit_balance': {'valid': False, 'details': {'exchange': 'bybit', 'fetch_timestamp': 1751376261.7727332, 'verification_timestamp': 1751376261.7727344, 'checks_performed': ['balance_freshness', 'balance_structure'], 'warnings': [], 'errors': ['No balances extracted from data'], 'balance_summary': {}, 'balance_timestamp': 1751376261.7725272, 'fetch_age_seconds': 3.814697265625e-06}}, 'bybit_real_balance': {'valid': True}}, 'overall_valid': False}}], 'warnings': [], 'statistics': {'real_data_validator': {'validator_status': 'ACTIVE', 'fail_fast_mode': True, 'max_data_age_seconds': 30, 'balance_freshness_seconds': 10, 'validation_history_count': 0, 'last_validation_time': 0, 'prohibited_patterns': {'fake_prices': [50000.0, 3000.0, 100.0, 0.5, 300.0, 0.6, 0.1], 'test_balances': [10000.0, 1000.0, 100.0], 'simulation_keywords': ['test', 'mock', 'fake', 'simulation', 'demo', 'sandbox'], 'hardcoded_symbols': ['BTC-USD', 'ETH-USD', 'SOL-USD']}}, 'api_validator': {'total_api_calls': 1, 'validation_failures': 0, 'success_rate_percent': 100.0, 'validator_status': 'ACTIVE', 'fail_fast_mode': True, 'max_response_age_seconds': 30, 'supported_exchanges': ['bybit', 'coinbase', 'binance']}, 'execution_validator': {'total_executions': 0, 'validation_failures': 0, 'success_rate_percent': 0.0, 'validator_status': 'ACTIVE', 'fail_fast_mode': True, 'max_execution_age_seconds': 60, 'recent_executions': 0}, 'balance_verifier': {'total_balance_checks': 1, 'validation_failures': 0, 'success_rate_percent': 100.0, 'validator_status': 'ACTIVE', 'fail_fast_mode': True, 'max_balance_age_seconds': 10, 'cached_exchanges': [], 'prohibited_balance_count': 18}, 'market_data_validator': {'total_validations': 0, 'validation_failures': 0, 'success_rate_percent': 0.0, 'validator_status': 'ACTIVE', 'fail_fast_mode': True, 'max_data_age_seconds': 30, 'supported_indicators': ['sma', 'ema', 'rsi', 'macd', 'bollinger_bands', 'volume_profile', 'price_action'], 'supported_neural_features': ['price_features', 'volume_features', 'technical_features', 'sentiment_features', 'market_microstructure']}, 'neural_validator': {'total_training_validations': 0, 'validation_failures': 0, 'success_rate_percent': 0.0, 'validator_status': 'ACTIVE', 'fail_fast_mode': True, 'max_training_data_age_hours': 24, 'validated_models_count': 0, 'validated_models': [], 'supported_training_types': ['trading_outcomes', 'market_movements', 'performance_data', 'feature_data', 'reward_signals']}, 'error_handler': {'total_errors': 0, 'critical_errors_total': 0, 'critical_errors_recent': 0, 'system_halted': False, 'halt_reason': None, 'last_critical_error': None, 'fail_fast_enabled': True, 'critical_error_threshold': 3, 'error_recovery_timeout': 300}, 'runtime_checkpoints': {'total_checkpoints': 0, 'validation_failures': 0, 'success_rate_percent': 0.0, 'validation_enabled': True, 'recent_checkpoints': 0, 'active_checkpoints': 0, 'max_data_age_seconds': 30, 'min_confidence_threshold': 0.6}, 'master_validator': {'total_validations': 1, 'total_failures': 0, 'success_rate_percent': 100.0, 'system_status': 'ACTIVE'}}, 'cross_validation': {'valid': True, 'checks': ['timestamp_consistency'], 'inconsistencies': []}}"}, "Fake Data Detection": {"passed": false, "error": "Fake data pattern 3 passed validation (should fail)"}, "System-Wide Validation": {"passed": false, "error": "System-wide validation failed: {'validation_timestamp': 1751376261.7762213, 'total_data_sources': 5, 'validation_results': {'api_responses': {'valid': True, 'details': {'pipeline': 'api_response', 'stages': {'bybit_api': {'valid': True, 'details': {'exchange': 'bybit', 'endpoint': 'live_endpoint', 'request_timestamp': 1751376261.7762353, 'validation_timestamp': 1751376261.7762363, 'checks_performed': ['response_structure', 'timestamp_freshness', 'endpoint_authenticity', 'exchange_specific', 'data_authenticity', 'response_consistency'], 'warnings': [], 'errors': [], 'response_timestamp': 1751376261.7761946, 'response_age_seconds': 5.14984130859375e-05, 'status': 'VALID'}}, 'bybit_real_data': {'valid': True}, 'coinbase_api': {'valid': True, 'details': {'exchange': 'coinbase', 'endpoint': 'live_endpoint', 'request_timestamp': 1751376261.7763753, 'validation_timestamp': 1751376261.7763758, 'checks_performed': ['response_structure', 'timestamp_freshness', 'endpoint_authenticity', 'exchange_specific', 'data_authenticity', 'response_consistency'], 'warnings': [], 'errors': [], 'response_timestamp': 1751376261.7761984, 'response_age_seconds': 0.0001811981201171875, 'status': 'VALID'}}, 'coinbase_real_data': {'valid': True}}, 'overall_valid': True}}, 'balance_data': {'valid': False, 'details': {'pipeline': 'balance_data', 'stages': {'bybit_balance': {'valid': False, 'details': {'exchange': 'bybit', 'fetch_timestamp': 1751376261.7764528, 'verification_timestamp': 1751376261.776454, 'checks_performed': ['balance_freshness', 'balance_structure'], 'warnings': [], 'errors': ['No balances extracted from data'], 'balance_summary': {}, 'balance_timestamp': 1751376261.7761996, 'fetch_age_seconds': 3.337860107421875e-06}}, 'bybit_real_balance': {'valid': True}}, 'overall_valid': False}}}, 'overall_status': 'SYSTEM_HALTED', 'critical_failures': [{'source_type': 'balance_data', 'details': {'pipeline': 'balance_data', 'stages': {'bybit_balance': {'valid': False, 'details': {'exchange': 'bybit', 'fetch_timestamp': 1751376261.7764528, 'verification_timestamp': 1751376261.776454, 'checks_performed': ['balance_freshness', 'balance_structure'], 'warnings': [], 'errors': ['No balances extracted from data'], 'balance_summary': {}, 'balance_timestamp': 1751376261.7761996, 'fetch_age_seconds': 3.337860107421875e-06}}, 'bybit_real_balance': {'valid': True}}, 'overall_valid': False}}], 'warnings': [], 'statistics': {}}"}}, "timestamp": "2025-07-01T15:24:21.788166", "validation_statistics": {"real_data_validator": {"validator_status": "ACTIVE", "fail_fast_mode": true, "max_data_age_seconds": 30, "balance_freshness_seconds": 10, "validation_history_count": 0, "last_validation_time": 0, "prohibited_patterns": {"fake_prices": [50000.0, 3000.0, 100.0, 0.5, 300.0, 0.6, 0.1], "test_balances": [10000.0, 1000.0, 100.0], "simulation_keywords": ["test", "mock", "fake", "simulation", "demo", "sandbox"], "hardcoded_symbols": ["BTC-USD", "ETH-USD", "SOL-USD"]}}, "api_validator": {"total_api_calls": 3, "validation_failures": 0, "success_rate_percent": 100.0, "validator_status": "ACTIVE", "fail_fast_mode": true, "max_response_age_seconds": 30, "supported_exchanges": ["bybit", "coinbase", "binance"]}, "execution_validator": {"total_executions": 0, "validation_failures": 0, "success_rate_percent": 0.0, "validator_status": "ACTIVE", "fail_fast_mode": true, "max_execution_age_seconds": 60, "recent_executions": 0}, "balance_verifier": {"total_balance_checks": 2, "validation_failures": 0, "success_rate_percent": 100.0, "validator_status": "ACTIVE", "fail_fast_mode": true, "max_balance_age_seconds": 10, "cached_exchanges": [], "prohibited_balance_count": 18}, "market_data_validator": {"total_validations": 1, "validation_failures": 0, "success_rate_percent": 100.0, "validator_status": "ACTIVE", "fail_fast_mode": true, "max_data_age_seconds": 30, "supported_indicators": ["sma", "ema", "rsi", "macd", "bollinger_bands", "volume_profile", "price_action"], "supported_neural_features": ["price_features", "volume_features", "technical_features", "sentiment_features", "market_microstructure"]}, "neural_validator": {"total_training_validations": 0, "validation_failures": 0, "success_rate_percent": 0.0, "validator_status": "ACTIVE", "fail_fast_mode": true, "max_training_data_age_hours": 24, "validated_models_count": 0, "validated_models": [], "supported_training_types": ["trading_outcomes", "market_movements", "performance_data", "feature_data", "reward_signals"]}, "error_handler": {"total_errors": 1, "critical_errors_total": 1, "critical_errors_recent": 1, "system_halted": true, "halt_reason": "Data validation failed", "last_critical_error": 1751376261.782774, "fail_fast_enabled": true, "critical_error_threshold": 3, "error_recovery_timeout": 300}, "runtime_checkpoints": {"total_checkpoints": 0, "validation_failures": 0, "success_rate_percent": 0.0, "validation_enabled": true, "recent_checkpoints": 0, "active_checkpoints": 0, "max_data_age_seconds": 30, "min_confidence_threshold": 0.6}, "master_validator": {"total_validations": 2, "total_failures": 1, "success_rate_percent": 50.0, "system_status": "ACTIVE"}}, "master_validator_report": {"master_validator_status": "ACTIVE", "validation_enabled": true, "fail_fast_mode": true, "total_validations": 2, "total_failures": 1, "recent_validations": 1, "system_halted": true, "halt_reason": "Data validation failed", "validation_components": {"real_data_validator": "ACTIVE", "api_validator": "ACTIVE", "execution_validator": "ACTIVE", "balance_verifier": "ACTIVE", "market_data_validator": "ACTIVE", "neural_validator": "ACTIVE", "error_handler": "ACTIVE", "runtime_checkpoints": "ACTIVE"}}}