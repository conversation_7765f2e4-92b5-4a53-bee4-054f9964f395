"""
Automatic Data Source Recovery - Re-establishes live data connections when issues are detected
"""

import time
import logging
import asyncio
import aiohttp
import websockets
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Union, Callable, Tuple
from decimal import Decimal
import json
import threading
from concurrent.futures import Thread<PERSON>oolExecutor
import random

logger = logging.getLogger(__name__)

class AutomaticDataSourceRecovery:
    """
    Intelligent system that automatically detects data source failures and
    re-establishes live connections to exchanges. Implements multiple recovery
    strategies and fallback mechanisms to ensure continuous data flow.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.recovery_enabled = True
        self.auto_failover_enabled = True
        
        # Recovery configuration
        self.max_recovery_attempts = 5
        self.recovery_backoff_base = 2  # Exponential backoff base
        self.recovery_timeout = 30
        self.health_check_interval = 15
        self.connection_timeout = 10
        
        # Data source management
        self.primary_sources = {}
        self.backup_sources = {}
        self.active_connections = {}
        self.failed_sources = set()
        self.recovery_queue = asyncio.Queue()
        
        # Recovery strategies
        self.recovery_strategies = {
            'immediate_reconnect': self._immediate_reconnect_strategy,
            'exponential_backoff': self._exponential_backoff_strategy,
            'circuit_breaker': self._circuit_breaker_strategy,
            'failover_cascade': self._failover_cascade_strategy,
            'load_balancing': self._load_balancing_strategy
        }
        
        # Exchange connection configurations
        self.exchange_configs = {
            'bybit': {
                'primary_endpoints': {
                    'websocket': 'wss://stream.bybit.com/v5/public/linear',
                    'rest_api': 'https://api.bybit.com/v5',
                    'private_ws': 'wss://stream.bybit.com/v5/private'
                },
                'backup_endpoints': {
                    'websocket': 'wss://stream.bybit.com/v5/public/spot',
                    'rest_api': 'https://api.bybit.com/v5',
                    'private_ws': 'wss://stream.bybit.com/v5/private'
                },
                'health_check_endpoint': '/v5/market/time',
                'connection_params': {
                    'ping_interval': 20,
                    'ping_timeout': 10,
                    'close_timeout': 10
                }
            },
            'coinbase': {
                'primary_endpoints': {
                    'websocket': 'wss://ws-feed.exchange.coinbase.com',
                    'rest_api': 'https://api.exchange.coinbase.com',
                    'private_ws': 'wss://ws-feed.exchange.coinbase.com'
                },
                'backup_endpoints': {
                    'websocket': 'wss://ws-feed.exchange.coinbase.com',
                    'rest_api': 'https://api.exchange.coinbase.com',
                    'private_ws': 'wss://ws-feed.exchange.coinbase.com'
                },
                'health_check_endpoint': '/time',
                'connection_params': {
                    'ping_interval': 30,
                    'ping_timeout': 10,
                    'close_timeout': 10
                }
            }
        }
        
        # Recovery statistics
        self.recovery_stats = {
            'total_recovery_attempts': 0,
            'successful_recoveries': 0,
            'failed_recoveries': 0,
            'failover_events': 0,
            'circuit_breaker_trips': 0,
            'average_recovery_time': 0.0,
            'uptime_percentage': 100.0
        }
        
        # Circuit breaker states
        self.circuit_breakers = {}
        
        # Thread pool for recovery operations
        self.recovery_executor = ThreadPoolExecutor(max_workers=5, thread_name_prefix="DataRecovery")
        
        logger.info("🔧 [DATA-SOURCE-RECOVERY] Initialized automatic data source recovery system")
    
    async def register_data_source(self, source_id: str, source_config: Dict[str, Any]) -> bool:
        """Register a data source for monitoring and recovery"""
        try:
            # Validate source configuration
            if not self._validate_source_config(source_config):
                raise ValueError(f"Invalid source configuration for {source_id}")
            
            # Initialize primary source
            self.primary_sources[source_id] = {
                'config': source_config,
                'status': 'INITIALIZING',
                'last_health_check': time.time(),
                'failure_count': 0,
                'recovery_attempts': 0,
                'connection': None
            }
            
            # Setup backup sources if available
            await self._setup_backup_sources(source_id, source_config)
            
            # Initialize circuit breaker
            self.circuit_breakers[source_id] = {
                'state': 'CLOSED',  # CLOSED, OPEN, HALF_OPEN
                'failure_count': 0,
                'last_failure_time': 0,
                'trip_threshold': 5,
                'recovery_timeout': 60
            }
            
            # Establish initial connection
            connection_success = await self._establish_connection(source_id, source_config)
            
            if connection_success:
                self.primary_sources[source_id]['status'] = 'ACTIVE'
                logger.info(f"✅ [DATA-SOURCE-RECOVERY] Registered and connected data source: {source_id}")
                return True
            else:
                self.primary_sources[source_id]['status'] = 'FAILED'
                logger.error(f"❌ [DATA-SOURCE-RECOVERY] Failed to establish initial connection: {source_id}")
                return False
                
        except Exception as e:
            logger.error(f"❌ [DATA-SOURCE-RECOVERY] Error registering data source {source_id}: {e}")
            return False
    
    async def detect_and_recover_source_failure(self, source_id: str, failure_details: Dict[str, Any]) -> bool:
        """Detect source failure and initiate recovery"""
        try:
            logger.warning(f"🚨 [DATA-SOURCE-RECOVERY] Source failure detected: {source_id} - {failure_details}")
            
            # Update source status
            if source_id in self.primary_sources:
                self.primary_sources[source_id]['status'] = 'FAILED'
                self.primary_sources[source_id]['failure_count'] += 1
                self.failed_sources.add(source_id)
            
            # Update circuit breaker
            await self._update_circuit_breaker(source_id, failure_details)
            
            # Queue recovery request
            recovery_request = {
                'source_id': source_id,
                'failure_details': failure_details,
                'timestamp': time.time(),
                'priority': self._calculate_recovery_priority(source_id, failure_details)
            }
            
            await self.recovery_queue.put(recovery_request)
            
            # Start immediate recovery if critical
            if failure_details.get('critical', False):
                return await self._execute_immediate_recovery(source_id, failure_details)
            
            return True
            
        except Exception as e:
            logger.error(f"❌ [DATA-SOURCE-RECOVERY] Error detecting source failure {source_id}: {e}")
            return False
    
    async def _execute_immediate_recovery(self, source_id: str, failure_details: Dict[str, Any]) -> bool:
        """Execute immediate recovery for critical failures"""
        try:
            logger.info(f"🚀 [DATA-SOURCE-RECOVERY] Executing immediate recovery for {source_id}")
            
            self.recovery_stats['total_recovery_attempts'] += 1
            recovery_start_time = time.time()
            
            # Try multiple recovery strategies
            recovery_strategies = ['immediate_reconnect', 'failover_cascade', 'circuit_breaker']
            
            for strategy_name in recovery_strategies:
                if strategy_name in self.recovery_strategies:
                    strategy = self.recovery_strategies[strategy_name]
                    
                    logger.info(f"🔧 [DATA-SOURCE-RECOVERY] Trying strategy: {strategy_name} for {source_id}")
                    
                    success = await strategy(source_id, failure_details)
                    
                    if success:
                        recovery_time = time.time() - recovery_start_time
                        self._update_recovery_stats(True, recovery_time)
                        
                        logger.info(f"✅ [DATA-SOURCE-RECOVERY] Recovery successful for {source_id} using {strategy_name} in {recovery_time:.2f}s")
                        return True
                    else:
                        logger.warning(f"⚠️ [DATA-SOURCE-RECOVERY] Strategy {strategy_name} failed for {source_id}")
            
            # All strategies failed
            self._update_recovery_stats(False, time.time() - recovery_start_time)
            logger.error(f"❌ [DATA-SOURCE-RECOVERY] All recovery strategies failed for {source_id}")
            return False
            
        except Exception as e:
            logger.error(f"❌ [DATA-SOURCE-RECOVERY] Immediate recovery error for {source_id}: {e}")
            return False
    
    async def _immediate_reconnect_strategy(self, source_id: str, failure_details: Dict[str, Any]) -> bool:
        """Immediate reconnection strategy"""
        try:
            logger.info(f"🔄 [DATA-SOURCE-RECOVERY] Immediate reconnect for {source_id}")
            
            # Close existing connection
            await self._close_connection(source_id)
            
            # Wait brief moment
            await asyncio.sleep(1)
            
            # Attempt reconnection
            if source_id in self.primary_sources:
                source_config = self.primary_sources[source_id]['config']
                return await self._establish_connection(source_id, source_config)
            
            return False
            
        except Exception as e:
            logger.error(f"❌ [DATA-SOURCE-RECOVERY] Immediate reconnect failed for {source_id}: {e}")
            return False
    
    async def _exponential_backoff_strategy(self, source_id: str, failure_details: Dict[str, Any]) -> bool:
        """Exponential backoff reconnection strategy"""
        try:
            logger.info(f"⏳ [DATA-SOURCE-RECOVERY] Exponential backoff for {source_id}")
            
            if source_id not in self.primary_sources:
                return False
            
            source_info = self.primary_sources[source_id]
            attempt = source_info['recovery_attempts']
            
            # Calculate backoff delay
            delay = min(self.recovery_backoff_base ** attempt, 60)  # Max 60 seconds
            
            logger.info(f"⏰ [DATA-SOURCE-RECOVERY] Waiting {delay}s before retry {attempt + 1} for {source_id}")
            await asyncio.sleep(delay)
            
            # Attempt reconnection
            source_config = source_info['config']
            success = await self._establish_connection(source_id, source_config)
            
            if success:
                source_info['recovery_attempts'] = 0  # Reset on success
            else:
                source_info['recovery_attempts'] += 1
            
            return success
            
        except Exception as e:
            logger.error(f"❌ [DATA-SOURCE-RECOVERY] Exponential backoff failed for {source_id}: {e}")
            return False
    
    async def _circuit_breaker_strategy(self, source_id: str, failure_details: Dict[str, Any]) -> bool:
        """Circuit breaker pattern for recovery"""
        try:
            if source_id not in self.circuit_breakers:
                return False
            
            breaker = self.circuit_breakers[source_id]
            current_time = time.time()
            
            if breaker['state'] == 'OPEN':
                # Check if recovery timeout has passed
                if current_time - breaker['last_failure_time'] > breaker['recovery_timeout']:
                    breaker['state'] = 'HALF_OPEN'
                    logger.info(f"🔄 [DATA-SOURCE-RECOVERY] Circuit breaker half-open for {source_id}")
                else:
                    logger.warning(f"⚠️ [DATA-SOURCE-RECOVERY] Circuit breaker still open for {source_id}")
                    return False
            
            if breaker['state'] in ['CLOSED', 'HALF_OPEN']:
                # Attempt connection
                if source_id in self.primary_sources:
                    source_config = self.primary_sources[source_id]['config']
                    success = await self._establish_connection(source_id, source_config)
                    
                    if success:
                        breaker['state'] = 'CLOSED'
                        breaker['failure_count'] = 0
                        logger.info(f"✅ [DATA-SOURCE-RECOVERY] Circuit breaker closed for {source_id}")
                        return True
                    else:
                        breaker['failure_count'] += 1
                        if breaker['failure_count'] >= breaker['trip_threshold']:
                            breaker['state'] = 'OPEN'
                            breaker['last_failure_time'] = current_time
                            self.recovery_stats['circuit_breaker_trips'] += 1
                            logger.warning(f"🔴 [DATA-SOURCE-RECOVERY] Circuit breaker opened for {source_id}")
            
            return False
            
        except Exception as e:
            logger.error(f"❌ [DATA-SOURCE-RECOVERY] Circuit breaker strategy failed for {source_id}: {e}")
            return False
    
    async def _failover_cascade_strategy(self, source_id: str, failure_details: Dict[str, Any]) -> bool:
        """Failover to backup sources"""
        try:
            logger.info(f"🔀 [DATA-SOURCE-RECOVERY] Failover cascade for {source_id}")
            
            # Try backup sources
            if source_id in self.backup_sources:
                backup_configs = self.backup_sources[source_id]
                
                for backup_id, backup_config in backup_configs.items():
                    logger.info(f"🔄 [DATA-SOURCE-RECOVERY] Trying backup source: {backup_id}")
                    
                    success = await self._establish_connection(f"{source_id}_backup_{backup_id}", backup_config)
                    
                    if success:
                        # Update active connection
                        self.active_connections[source_id] = f"{source_id}_backup_{backup_id}"
                        self.recovery_stats['failover_events'] += 1
                        
                        logger.info(f"✅ [DATA-SOURCE-RECOVERY] Failover successful to backup: {backup_id}")
                        return True
            
            logger.warning(f"⚠️ [DATA-SOURCE-RECOVERY] No backup sources available for {source_id}")
            return False
            
        except Exception as e:
            logger.error(f"❌ [DATA-SOURCE-RECOVERY] Failover cascade failed for {source_id}: {e}")
            return False
    
    async def _load_balancing_strategy(self, source_id: str, failure_details: Dict[str, Any]) -> bool:
        """Load balancing across multiple endpoints"""
        try:
            logger.info(f"⚖️ [DATA-SOURCE-RECOVERY] Load balancing for {source_id}")
            
            # Get exchange configuration
            exchange = self._extract_exchange_from_source_id(source_id)
            if exchange not in self.exchange_configs:
                return False
            
            config = self.exchange_configs[exchange]
            
            # Try different endpoints
            endpoints = list(config['primary_endpoints'].values()) + list(config['backup_endpoints'].values())
            random.shuffle(endpoints)  # Randomize for load balancing
            
            for endpoint in endpoints:
                try:
                    # Test endpoint health
                    if await self._test_endpoint_health(endpoint, exchange):
                        # Create new connection config
                        new_config = self.primary_sources[source_id]['config'].copy()
                        new_config['endpoint'] = endpoint
                        
                        success = await self._establish_connection(source_id, new_config)
                        if success:
                            logger.info(f"✅ [DATA-SOURCE-RECOVERY] Load balanced to endpoint: {endpoint}")
                            return True
                            
                except Exception as e:
                    logger.warning(f"⚠️ [DATA-SOURCE-RECOVERY] Endpoint {endpoint} failed: {e}")
                    continue
            
            return False
            
        except Exception as e:
            logger.error(f"❌ [DATA-SOURCE-RECOVERY] Load balancing failed for {source_id}: {e}")
            return False
    
    async def _establish_connection(self, source_id: str, source_config: Dict[str, Any]) -> bool:
        """Establish connection to data source"""
        try:
            connection_type = source_config.get('type', 'websocket')
            
            if connection_type == 'websocket':
                return await self._establish_websocket_connection(source_id, source_config)
            elif connection_type == 'rest':
                return await self._establish_rest_connection(source_id, source_config)
            else:
                logger.error(f"❌ [DATA-SOURCE-RECOVERY] Unknown connection type: {connection_type}")
                return False
                
        except Exception as e:
            logger.error(f"❌ [DATA-SOURCE-RECOVERY] Connection establishment failed for {source_id}: {e}")
            return False
    
    async def _establish_websocket_connection(self, source_id: str, source_config: Dict[str, Any]) -> bool:
        """Establish WebSocket connection"""
        try:
            endpoint = source_config.get('endpoint', '')
            
            # Validate endpoint is not test/sandbox
            if self._is_test_endpoint(endpoint):
                raise ValueError(f"Test endpoint not allowed: {endpoint}")
            
            # Create WebSocket connection
            websocket = await websockets.connect(
                endpoint,
                ping_interval=20,
                ping_timeout=10,
                close_timeout=10
            )
            
            # Store connection
            self.active_connections[source_id] = websocket
            
            # Send subscription if needed
            subscription = source_config.get('subscription')
            if subscription:
                await websocket.send(json.dumps(subscription))
            
            logger.info(f"🌐 [DATA-SOURCE-RECOVERY] WebSocket connected: {source_id} -> {endpoint}")
            return True
            
        except Exception as e:
            logger.error(f"❌ [DATA-SOURCE-RECOVERY] WebSocket connection failed for {source_id}: {e}")
            return False
    
    async def _establish_rest_connection(self, source_id: str, source_config: Dict[str, Any]) -> bool:
        """Establish REST API connection"""
        try:
            base_url = source_config.get('base_url', '')
            health_endpoint = source_config.get('health_endpoint', '/time')
            
            # Validate endpoint is not test/sandbox
            if self._is_test_endpoint(base_url):
                raise ValueError(f"Test endpoint not allowed: {base_url}")
            
            # Test connection
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{base_url}{health_endpoint}",
                    timeout=aiohttp.ClientTimeout(total=self.connection_timeout)
                ) as response:
                    if response.status == 200:
                        # Store connection info
                        self.active_connections[source_id] = {
                            'type': 'rest',
                            'base_url': base_url,
                            'session': session
                        }
                        
                        logger.info(f"🔗 [DATA-SOURCE-RECOVERY] REST connected: {source_id} -> {base_url}")
                        return True
                    else:
                        logger.error(f"❌ [DATA-SOURCE-RECOVERY] REST health check failed: {response.status}")
                        return False
                        
        except Exception as e:
            logger.error(f"❌ [DATA-SOURCE-RECOVERY] REST connection failed for {source_id}: {e}")
            return False
    
    async def _test_endpoint_health(self, endpoint: str, exchange: str) -> bool:
        """Test endpoint health"""
        try:
            if exchange in self.exchange_configs:
                health_endpoint = self.exchange_configs[exchange]['health_check_endpoint']
                
                async with aiohttp.ClientSession() as session:
                    async with session.get(
                        f"{endpoint}{health_endpoint}",
                        timeout=aiohttp.ClientTimeout(total=5)
                    ) as response:
                        return response.status == 200
            
            return False
            
        except Exception:
            return False
    
    def _is_test_endpoint(self, endpoint: str) -> bool:
        """Check if endpoint is test/sandbox"""
        test_indicators = ['testnet', 'sandbox', 'test', 'demo', 'localhost', '127.0.0.1']
        endpoint_lower = endpoint.lower()
        
        return any(indicator in endpoint_lower for indicator in test_indicators)
    
    def _validate_source_config(self, config: Dict[str, Any]) -> bool:
        """Validate source configuration"""
        required_fields = ['type', 'endpoint']
        return all(field in config for field in required_fields)
    
    async def _setup_backup_sources(self, source_id: str, source_config: Dict[str, Any]):
        """Setup backup sources for failover"""
        exchange = self._extract_exchange_from_source_id(source_id)
        
        if exchange in self.exchange_configs:
            backup_endpoints = self.exchange_configs[exchange]['backup_endpoints']
            
            self.backup_sources[source_id] = {}
            
            for backup_name, backup_endpoint in backup_endpoints.items():
                backup_config = source_config.copy()
                backup_config['endpoint'] = backup_endpoint
                
                self.backup_sources[source_id][backup_name] = backup_config
    
    def _extract_exchange_from_source_id(self, source_id: str) -> str:
        """Extract exchange name from source ID"""
        for exchange in self.exchange_configs.keys():
            if exchange in source_id.lower():
                return exchange
        return 'unknown'
    
    async def _close_connection(self, source_id: str):
        """Close connection for source"""
        if source_id in self.active_connections:
            connection = self.active_connections[source_id]
            
            try:
                if hasattr(connection, 'close'):
                    await connection.close()
                elif isinstance(connection, dict) and connection.get('type') == 'rest':
                    # REST connections are handled by session context managers
                    pass
                
                del self.active_connections[source_id]
                
            except Exception as e:
                logger.error(f"❌ [DATA-SOURCE-RECOVERY] Error closing connection {source_id}: {e}")
    
    async def _update_circuit_breaker(self, source_id: str, failure_details: Dict[str, Any]):
        """Update circuit breaker state"""
        if source_id in self.circuit_breakers:
            breaker = self.circuit_breakers[source_id]
            breaker['failure_count'] += 1
            breaker['last_failure_time'] = time.time()
            
            if breaker['failure_count'] >= breaker['trip_threshold'] and breaker['state'] == 'CLOSED':
                breaker['state'] = 'OPEN'
                self.recovery_stats['circuit_breaker_trips'] += 1
                logger.warning(f"🔴 [DATA-SOURCE-RECOVERY] Circuit breaker tripped for {source_id}")
    
    def _calculate_recovery_priority(self, source_id: str, failure_details: Dict[str, Any]) -> int:
        """Calculate recovery priority (higher = more urgent)"""
        priority = 1
        
        if failure_details.get('critical', False):
            priority += 10
        
        if 'trading' in source_id.lower():
            priority += 5
        
        if 'balance' in source_id.lower():
            priority += 3
        
        return priority
    
    def _update_recovery_stats(self, success: bool, recovery_time: float):
        """Update recovery statistics"""
        if success:
            self.recovery_stats['successful_recoveries'] += 1
        else:
            self.recovery_stats['failed_recoveries'] += 1
        
        # Update average recovery time
        total_recoveries = self.recovery_stats['successful_recoveries'] + self.recovery_stats['failed_recoveries']
        if total_recoveries > 0:
            current_avg = self.recovery_stats['average_recovery_time']
            self.recovery_stats['average_recovery_time'] = (
                (current_avg * (total_recoveries - 1) + recovery_time) / total_recoveries
            )
    
    def get_recovery_statistics(self) -> Dict[str, Any]:
        """Get comprehensive recovery statistics"""
        total_sources = len(self.primary_sources)
        active_sources = len([s for s in self.primary_sources.values() if s['status'] == 'ACTIVE'])
        
        uptime_percentage = (active_sources / max(total_sources, 1)) * 100
        self.recovery_stats['uptime_percentage'] = uptime_percentage
        
        return {
            **self.recovery_stats,
            'total_registered_sources': total_sources,
            'active_sources': active_sources,
            'failed_sources': len(self.failed_sources),
            'circuit_breaker_states': {sid: cb['state'] for sid, cb in self.circuit_breakers.items()},
            'recovery_queue_size': self.recovery_queue.qsize(),
            'active_connections': len(self.active_connections)
        }
