"""
Runtime Validation Checkpoints - Comprehensive runtime assertions for data authenticity
"""

import time
import logging
import asyncio
import functools
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Union, Callable, Tuple
from decimal import Decimal
import json
import hashlib
import inspect

logger = logging.getLogger(__name__)

class RuntimeValidationCheckpoints:
    """
    Implements comprehensive runtime assertions for data authenticity, API response validation,
    timestamp checks, and data source traceability throughout the trading system.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.validation_enabled = True
        self.checkpoint_count = 0
        self.validation_failures = 0
        self.checkpoint_history = []
        
        # Validation checkpoint registry
        self.checkpoints = {}
        self.checkpoint_stats = {}
        
        # Runtime assertion settings
        self.max_data_age_seconds = 30
        self.min_confidence_threshold = 0.6
        self.required_data_sources = ['live_api', 'real_exchange']
        
        logger.info("🔒 [RUNTIME-VALIDATION] Initialized runtime validation checkpoints")
    
    def checkpoint(self, checkpoint_name: str, validation_rules: Optional[Dict[str, Any]] = None):
        """
        Decorator for creating validation checkpoints
        
        Args:
            checkpoint_name: Name of the checkpoint
            validation_rules: Optional validation rules for this checkpoint
        """
        def decorator(func: Callable) -> Callable:
            @functools.wraps(func)
            async def async_wrapper(*args, **kwargs):
                # Pre-execution validation
                await self._pre_execution_checkpoint(checkpoint_name, func, args, kwargs, validation_rules)
                
                # Execute function
                result = await func(*args, **kwargs)
                
                # Post-execution validation
                await self._post_execution_checkpoint(checkpoint_name, func, result, validation_rules)
                
                return result
            
            @functools.wraps(func)
            def sync_wrapper(*args, **kwargs):
                # Pre-execution validation (sync)
                asyncio.create_task(self._pre_execution_checkpoint(checkpoint_name, func, args, kwargs, validation_rules))
                
                # Execute function
                result = func(*args, **kwargs)
                
                # Post-execution validation (sync)
                asyncio.create_task(self._post_execution_checkpoint(checkpoint_name, func, result, validation_rules))
                
                return result
            
            # Return appropriate wrapper based on function type
            if inspect.iscoroutinefunction(func):
                return async_wrapper
            else:
                return sync_wrapper
        
        return decorator
    
    async def _pre_execution_checkpoint(self, checkpoint_name: str, func: Callable, 
                                      args: tuple, kwargs: dict, validation_rules: Optional[Dict[str, Any]]):
        """Perform pre-execution validation checkpoint"""
        checkpoint_id = f"{checkpoint_name}_{int(time.time() * 1000)}"
        
        checkpoint_data = {
            'checkpoint_id': checkpoint_id,
            'checkpoint_name': checkpoint_name,
            'function_name': func.__name__,
            'timestamp': time.time(),
            'phase': 'pre_execution',
            'args_count': len(args),
            'kwargs_keys': list(kwargs.keys()),
            'validation_rules': validation_rules or {}
        }
        
        try:
            self.checkpoint_count += 1
            
            # Validate input arguments
            await self._validate_input_arguments(args, kwargs, checkpoint_data)
            
            # Apply custom validation rules
            if validation_rules:
                await self._apply_validation_rules(validation_rules, {'args': args, 'kwargs': kwargs}, checkpoint_data)
            
            # Store checkpoint
            self.checkpoints[checkpoint_id] = checkpoint_data
            
            logger.debug(f"✅ [CHECKPOINT-PRE] {checkpoint_name}: {func.__name__}")
            
        except Exception as e:
            self.validation_failures += 1
            checkpoint_data['error'] = str(e)
            checkpoint_data['status'] = 'FAILED'
            
            logger.error(f"❌ [CHECKPOINT-PRE-FAILED] {checkpoint_name}: {e}")
            raise RuntimeError(f"CRITICAL: Pre-execution checkpoint failed - {e}")
    
    async def _post_execution_checkpoint(self, checkpoint_name: str, func: Callable, 
                                       result: Any, validation_rules: Optional[Dict[str, Any]]):
        """Perform post-execution validation checkpoint"""
        # Find corresponding pre-execution checkpoint
        checkpoint_id = None
        for cid, cdata in self.checkpoints.items():
            if (cdata['checkpoint_name'] == checkpoint_name and 
                cdata['function_name'] == func.__name__ and
                cdata['phase'] == 'pre_execution' and
                'post_validated' not in cdata):
                checkpoint_id = cid
                break
        
        if not checkpoint_id:
            logger.warning(f"⚠️ [CHECKPOINT-POST] No pre-execution checkpoint found for {checkpoint_name}")
            return
        
        checkpoint_data = self.checkpoints[checkpoint_id]
        checkpoint_data['post_execution_timestamp'] = time.time()
        checkpoint_data['execution_time'] = checkpoint_data['post_execution_timestamp'] - checkpoint_data['timestamp']
        checkpoint_data['result_type'] = type(result).__name__
        checkpoint_data['post_validated'] = True
        
        try:
            # Validate result data
            await self._validate_result_data(result, checkpoint_data)
            
            # Apply post-execution validation rules
            if validation_rules and 'post_execution' in validation_rules:
                await self._apply_validation_rules(
                    validation_rules['post_execution'], 
                    {'result': result}, 
                    checkpoint_data
                )
            
            checkpoint_data['status'] = 'PASSED'
            logger.debug(f"✅ [CHECKPOINT-POST] {checkpoint_name}: {func.__name__} ({checkpoint_data['execution_time']:.3f}s)")
            
        except Exception as e:
            self.validation_failures += 1
            checkpoint_data['error'] = str(e)
            checkpoint_data['status'] = 'FAILED'
            
            logger.error(f"❌ [CHECKPOINT-POST-FAILED] {checkpoint_name}: {e}")
            raise RuntimeError(f"CRITICAL: Post-execution checkpoint failed - {e}")
    
    async def _validate_input_arguments(self, args: tuple, kwargs: dict, checkpoint_data: Dict[str, Any]):
        """Validate input arguments for authenticity"""
        validation_results = []
        
        # Check all arguments for prohibited patterns
        all_args = list(args) + list(kwargs.values())
        
        for i, arg in enumerate(all_args):
            if isinstance(arg, dict):
                # Validate dictionary arguments (likely data structures)
                arg_validation = await self._validate_data_structure(arg, f"arg_{i}")
                validation_results.append(arg_validation)
            
            elif isinstance(arg, (int, float, Decimal)):
                # Validate numeric arguments
                arg_validation = self._validate_numeric_value(arg, f"arg_{i}")
                validation_results.append(arg_validation)
            
            elif isinstance(arg, str):
                # Validate string arguments
                arg_validation = self._validate_string_value(arg, f"arg_{i}")
                validation_results.append(arg_validation)
        
        # Check if any validations failed
        failed_validations = [v for v in validation_results if not v['valid']]
        if failed_validations:
            checkpoint_data['input_validation_failures'] = failed_validations
            raise ValueError(f"Input validation failed: {failed_validations}")
        
        checkpoint_data['input_validation_results'] = validation_results
    
    async def _validate_result_data(self, result: Any, checkpoint_data: Dict[str, Any]):
        """Validate result data for authenticity"""
        if isinstance(result, dict):
            # Validate dictionary results (API responses, data structures)
            await self._validate_api_response_authenticity(result, checkpoint_data)
        
        elif isinstance(result, (list, tuple)):
            # Validate list/tuple results
            for i, item in enumerate(result):
                if isinstance(item, dict):
                    await self._validate_data_structure(item, f"result_item_{i}")
        
        elif isinstance(result, (int, float, Decimal)):
            # Validate numeric results
            self._validate_numeric_value(result, "result")
        
        checkpoint_data['result_validated'] = True
    
    async def _validate_data_structure(self, data: Dict[str, Any], context: str) -> Dict[str, Any]:
        """Validate data structure for authenticity"""
        validation_result = {
            'context': context,
            'valid': True,
            'warnings': [],
            'errors': []
        }
        
        # Check for timestamp freshness
        timestamp = self._extract_timestamp(data)
        if timestamp:
            age_seconds = time.time() - timestamp
            if age_seconds > self.max_data_age_seconds:
                validation_result['errors'].append(f"Data too old: {age_seconds:.1f}s")
                validation_result['valid'] = False
        
        # Check for prohibited patterns
        data_str = json.dumps(data, default=str).lower()
        prohibited_patterns = ['mock', 'fake', 'test', 'simulation', 'demo', 'synthetic']
        
        for pattern in prohibited_patterns:
            if pattern in data_str:
                validation_result['errors'].append(f"Prohibited pattern: {pattern}")
                validation_result['valid'] = False
        
        # Check for data source traceability
        if 'data_source' not in data and 'source' not in data:
            validation_result['warnings'].append("No data source traceability")
        
        return validation_result
    
    async def _validate_api_response_authenticity(self, response: Dict[str, Any], checkpoint_data: Dict[str, Any]):
        """Validate API response authenticity"""
        # Check for error responses
        if 'error' in response or 'errors' in response:
            error_msg = response.get('error') or response.get('errors')
            raise ValueError(f"API response contains error: {error_msg}")
        
        # Check for required fields
        required_fields = ['timestamp', 'data']
        missing_fields = [field for field in required_fields if field not in response]
        
        if missing_fields:
            checkpoint_data['missing_api_fields'] = missing_fields
        
        # Validate response timestamp
        response_timestamp = self._extract_timestamp(response)
        if response_timestamp:
            age_seconds = time.time() - response_timestamp
            if age_seconds > self.max_data_age_seconds:
                raise ValueError(f"API response too old: {age_seconds:.1f}s")
        
        checkpoint_data['api_response_validated'] = True
    
    def _validate_numeric_value(self, value: Union[int, float, Decimal], context: str) -> Dict[str, Any]:
        """Validate numeric value for authenticity"""
        validation_result = {
            'context': context,
            'valid': True,
            'warnings': [],
            'errors': []
        }
        
        value_float = float(value)
        
        # Check for prohibited placeholder values
        prohibited_values = [0.0, 1.0, 100.0, 1000.0, 50000.0, 3000.0]
        if value_float in prohibited_values:
            validation_result['errors'].append(f"Prohibited placeholder value: {value_float}")
            validation_result['valid'] = False
        
        # Check for unrealistic values
        if value_float < 0 and context.startswith('price'):
            validation_result['errors'].append(f"Negative price value: {value_float}")
            validation_result['valid'] = False
        
        return validation_result
    
    def _validate_string_value(self, value: str, context: str) -> Dict[str, Any]:
        """Validate string value for authenticity"""
        validation_result = {
            'context': context,
            'valid': True,
            'warnings': [],
            'errors': []
        }
        
        value_lower = value.lower()
        
        # Check for prohibited placeholder strings
        prohibited_strings = ['n/a', 'null', 'undefined', 'placeholder', 'mock', 'test', 'fake']
        if value_lower in prohibited_strings:
            validation_result['errors'].append(f"Prohibited placeholder string: {value}")
            validation_result['valid'] = False
        
        return validation_result
    
    async def _apply_validation_rules(self, rules: Dict[str, Any], data: Dict[str, Any], 
                                    checkpoint_data: Dict[str, Any]):
        """Apply custom validation rules"""
        for rule_name, rule_config in rules.items():
            try:
                if rule_name == 'timestamp_freshness':
                    max_age = rule_config.get('max_age_seconds', self.max_data_age_seconds)
                    await self._validate_timestamp_freshness(data, max_age)
                
                elif rule_name == 'confidence_threshold':
                    min_confidence = rule_config.get('min_confidence', self.min_confidence_threshold)
                    await self._validate_confidence_threshold(data, min_confidence)
                
                elif rule_name == 'data_source_required':
                    required_sources = rule_config.get('sources', self.required_data_sources)
                    await self._validate_required_data_sources(data, required_sources)
                
                elif rule_name == 'no_simulation_mode':
                    await self._validate_no_simulation_mode(data)
                
                elif rule_name == 'balance_authenticity':
                    await self._validate_balance_authenticity(data)
                
                elif rule_name == 'trading_execution_real':
                    await self._validate_trading_execution_real(data)
                
            except Exception as e:
                checkpoint_data['rule_failures'] = checkpoint_data.get('rule_failures', [])
                checkpoint_data['rule_failures'].append({
                    'rule': rule_name,
                    'error': str(e)
                })
                raise ValueError(f"Validation rule '{rule_name}' failed: {e}")
    
    async def _validate_timestamp_freshness(self, data: Dict[str, Any], max_age_seconds: int):
        """Validate timestamp freshness"""
        for key, value in data.items():
            if isinstance(value, dict):
                timestamp = self._extract_timestamp(value)
                if timestamp:
                    age_seconds = time.time() - timestamp
                    if age_seconds > max_age_seconds:
                        raise ValueError(f"Timestamp too old in {key}: {age_seconds:.1f}s > {max_age_seconds}s")
    
    async def _validate_confidence_threshold(self, data: Dict[str, Any], min_confidence: float):
        """Validate confidence threshold"""
        for key, value in data.items():
            if isinstance(value, dict) and 'confidence' in value:
                confidence = float(value['confidence'])
                if confidence < min_confidence:
                    raise ValueError(f"Confidence too low in {key}: {confidence} < {min_confidence}")
    
    async def _validate_required_data_sources(self, data: Dict[str, Any], required_sources: List[str]):
        """Validate required data sources are present"""
        for key, value in data.items():
            if isinstance(value, dict):
                data_source = value.get('data_source') or value.get('source', '').lower()
                if data_source and not any(req_source in data_source for req_source in required_sources):
                    raise ValueError(f"Invalid data source in {key}: {data_source}")
    
    async def _validate_no_simulation_mode(self, data: Dict[str, Any]):
        """Validate no simulation mode indicators"""
        data_str = json.dumps(data, default=str).lower()
        simulation_indicators = ['simulation', 'demo', 'test', 'mock', 'sandbox', 'paper']
        
        for indicator in simulation_indicators:
            if indicator in data_str:
                raise ValueError(f"Simulation mode indicator detected: {indicator}")
    
    async def _validate_balance_authenticity(self, data: Dict[str, Any]):
        """Validate balance data authenticity"""
        for key, value in data.items():
            if 'balance' in key.lower() and isinstance(value, dict):
                # Check for real balance structure
                if 'balances' in value:
                    balances = value['balances']
                    for currency, amount in balances.items():
                        if isinstance(amount, (int, float, Decimal)):
                            amount_float = float(amount)
                            # Check for test balance amounts
                            if amount_float in [10000.0, 1000.0, 100.0]:
                                raise ValueError(f"Test balance amount detected: {currency}={amount_float}")
    
    async def _validate_trading_execution_real(self, data: Dict[str, Any]):
        """Validate trading execution is real"""
        for key, value in data.items():
            if isinstance(value, dict) and ('order' in key.lower() or 'trade' in key.lower()):
                # Check for transaction ID
                tx_id = value.get('transaction_id') or value.get('order_id')
                if not tx_id:
                    raise ValueError(f"No transaction ID in trading execution: {key}")
                
                # Check for fake transaction ID patterns
                tx_id_str = str(tx_id).lower()
                fake_patterns = ['mock', 'fake', 'test', '12345', 'abcdef']
                if any(pattern in tx_id_str for pattern in fake_patterns):
                    raise ValueError(f"Fake transaction ID detected: {tx_id}")
    
    def _extract_timestamp(self, data: Dict[str, Any]) -> Optional[float]:
        """Extract timestamp from data structure"""
        timestamp_fields = ['timestamp', 'time', 'created_at', 'updated_at', 'server_time']
        
        for field in timestamp_fields:
            if field in data:
                timestamp = data[field]
                try:
                    if isinstance(timestamp, (int, float)):
                        if timestamp > 1e12:  # Milliseconds
                            return timestamp / 1000
                        else:  # Seconds
                            return float(timestamp)
                    elif isinstance(timestamp, str):
                        dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                        return dt.timestamp()
                except (ValueError, TypeError):
                    continue
        
        return None
    
    def assert_data_authenticity(self, data: Any, context: str = "unknown"):
        """Runtime assertion for data authenticity"""
        if not self.validation_enabled:
            return
        
        try:
            if isinstance(data, dict):
                # Check for prohibited patterns
                data_str = json.dumps(data, default=str).lower()
                prohibited = ['mock', 'fake', 'test', 'simulation']
                
                for pattern in prohibited:
                    if pattern in data_str:
                        raise AssertionError(f"Prohibited data pattern '{pattern}' in {context}")
                
                # Check timestamp freshness
                timestamp = self._extract_timestamp(data)
                if timestamp:
                    age = time.time() - timestamp
                    if age > self.max_data_age_seconds:
                        raise AssertionError(f"Stale data in {context}: {age:.1f}s old")
            
            elif isinstance(data, (int, float, Decimal)):
                # Check for placeholder values
                value_float = float(data)
                prohibited_values = [0.0, 1.0, 100.0, 1000.0, 50000.0]
                
                if value_float in prohibited_values:
                    raise AssertionError(f"Placeholder value {value_float} in {context}")
            
        except Exception as e:
            logger.error(f"❌ [ASSERTION-FAILED] Data authenticity assertion failed: {e}")
            raise
    
    def get_checkpoint_statistics(self) -> Dict[str, Any]:
        """Get checkpoint validation statistics"""
        success_rate = ((self.checkpoint_count - self.validation_failures) / 
                       max(self.checkpoint_count, 1)) * 100
        
        recent_checkpoints = [
            cp for cp in self.checkpoints.values() 
            if time.time() - cp['timestamp'] < 3600  # Last hour
        ]
        
        return {
            'total_checkpoints': self.checkpoint_count,
            'validation_failures': self.validation_failures,
            'success_rate_percent': round(success_rate, 2),
            'validation_enabled': self.validation_enabled,
            'recent_checkpoints': len(recent_checkpoints),
            'active_checkpoints': len(self.checkpoints),
            'max_data_age_seconds': self.max_data_age_seconds,
            'min_confidence_threshold': self.min_confidence_threshold
        }
