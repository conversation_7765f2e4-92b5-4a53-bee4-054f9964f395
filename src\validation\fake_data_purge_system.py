"""
Fake Data Purge System - Identifies, isolates, and removes fake/mock/simulated data
"""

import time
import logging
import asyncio
import re
import json
import hashlib
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Union, Tuple, Set
from decimal import Decimal
import threading
from concurrent.futures import ThreadPoolExecutor

logger = logging.getLogger(__name__)

class FakeDataPurgeSystem:
    """
    Advanced system for identifying, isolating, and removing fake, mock, or simulated data
    while preserving authentic real data. Implements intelligent pattern recognition
    and surgical data removal techniques.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.purge_enabled = True
        self.aggressive_purging = True
        self.preserve_real_data = True
        
        # Purge configuration
        self.scan_interval = 30  # Scan for fake data every 30 seconds
        self.quarantine_duration = 300  # Keep quarantined data for 5 minutes
        self.max_purge_batch_size = 100
        
        # Fake data detection patterns
        self.fake_patterns = {
            'hardcoded_values': {
                'prices': [50000.0, 3000.0, 100.0, 1000.0, 10000.0, 0.5, 0.1, 1.0],
                'balances': [10000.0, 1000.0, 100.0, 10.0, 1.0, 0.1],
                'volumes': [1000000.0, 100000.0, 10000.0, 1000.0],
                'percentages': [0.5, 0.1, 0.01, 1.0, 10.0, 50.0, 100.0]
            },
            'string_indicators': [
                'mock', 'fake', 'test', 'simulation', 'demo', 'sandbox',
                'placeholder', 'dummy', 'sample', 'generated', 'synthetic',
                'artificial', 'simulated', 'virtual', 'paper', 'testnet'
            ],
            'field_patterns': [
                r'.*mock.*', r'.*fake.*', r'.*test.*', r'.*demo.*',
                r'.*simulation.*', r'.*synthetic.*', r'.*generated.*',
                r'.*placeholder.*', r'.*dummy.*', r'.*sample.*'
            ],
            'url_patterns': [
                r'.*testnet.*', r'.*sandbox.*', r'.*test\..*', r'.*demo\..*',
                r'.*localhost.*', r'.*127\.0\.0\.1.*', r'.*192\.168\..*'
            ],
            'timestamp_patterns': {
                'too_old_threshold': 300,  # 5 minutes
                'future_threshold': 60,    # 1 minute in future
                'round_numbers': [0, 1000000000, 1500000000, 1600000000]  # Obviously fake timestamps
            }
        }
        
        # Data preservation rules
        self.preservation_rules = {
            'required_fields': ['timestamp', 'data_source', 'exchange'],
            'authentic_sources': ['live_api', 'live_stream', 'real_exchange'],
            'valid_exchanges': ['bybit', 'coinbase', 'binance'],
            'minimum_data_age': 1,  # Data must be at least 1 second old to be considered real
            'maximum_data_age': 300  # Data older than 5 minutes is suspicious
        }
        
        # Purge tracking
        self.purged_data_log = []
        self.quarantined_data = {}
        self.purge_statistics = {
            'total_scans': 0,
            'fake_data_detected': 0,
            'data_purged': 0,
            'data_quarantined': 0,
            'false_positives': 0,
            'real_data_preserved': 0
        }
        
        # Active data stores
        self.monitored_data_stores = {}
        self.purge_queue = asyncio.Queue()
        
        # Thread pool for purge operations
        self.purge_executor = ThreadPoolExecutor(max_workers=3, thread_name_prefix="DataPurge")
        
        # Purge lock for thread safety
        self.purge_lock = threading.Lock()
        
        logger.info("🗑️ [FAKE-DATA-PURGE] Initialized fake data purge system")
    
    async def scan_and_purge_fake_data(self, data_store: Dict[str, Any], 
                                     store_name: str) -> Tuple[Dict[str, Any], List[str]]:
        """
        Scan data store for fake data and purge it while preserving real data
        
        Returns:
            Tuple[Dict[str, Any], List[str]]: (cleaned_data, purge_report)
        """
        purge_session = {
            'session_id': f"purge_{int(time.time() * 1000)}",
            'store_name': store_name,
            'start_time': time.time(),
            'original_data_count': len(data_store),
            'fake_items_found': [],
            'purged_items': [],
            'preserved_items': [],
            'status': 'IN_PROGRESS'
        }
        
        try:
            logger.info(f"🔍 [FAKE-DATA-PURGE] Scanning {store_name} for fake data ({len(data_store)} items)")
            
            self.purge_statistics['total_scans'] += 1
            
            # Create cleaned data store
            cleaned_data = {}
            purge_report = []
            
            # Scan each item in the data store
            for key, value in data_store.items():
                scan_result = await self._scan_data_item(key, value, store_name)
                
                if scan_result['is_fake']:
                    # Handle fake data
                    await self._handle_fake_data(key, value, scan_result, purge_session)
                    purge_report.append(f"PURGED: {key} - {scan_result['fake_reasons']}")
                    
                elif scan_result['is_suspicious']:
                    # Quarantine suspicious data
                    await self._quarantine_suspicious_data(key, value, scan_result, purge_session)
                    purge_report.append(f"QUARANTINED: {key} - {scan_result['suspicious_reasons']}")
                    
                else:
                    # Preserve real data
                    cleaned_data[key] = value
                    purge_session['preserved_items'].append(key)
                    self.purge_statistics['real_data_preserved'] += 1
            
            purge_session['status'] = 'COMPLETED'
            purge_session['end_time'] = time.time()
            purge_session['cleaned_data_count'] = len(cleaned_data)
            purge_session['duration'] = purge_session['end_time'] - purge_session['start_time']
            
            # Log purge session
            self.purged_data_log.append(purge_session)
            
            logger.info(f"✅ [FAKE-DATA-PURGE] Completed purge of {store_name}: "
                       f"{len(purge_session['purged_items'])} purged, "
                       f"{len(purge_session['preserved_items'])} preserved")
            
            return cleaned_data, purge_report
            
        except Exception as e:
            purge_session['status'] = 'FAILED'
            purge_session['error'] = str(e)
            purge_session['end_time'] = time.time()
            
            logger.error(f"❌ [FAKE-DATA-PURGE] Purge failed for {store_name}: {e}")
            return data_store, [f"ERROR: Purge failed - {e}"]
    
    async def _scan_data_item(self, key: str, value: Any, store_name: str) -> Dict[str, Any]:
        """Scan individual data item for fake patterns"""
        scan_result = {
            'is_fake': False,
            'is_suspicious': False,
            'fake_reasons': [],
            'suspicious_reasons': [],
            'confidence_score': 0.0,
            'scan_details': {}
        }
        
        try:
            # Convert value to scannable format
            if isinstance(value, dict):
                scan_data = value
            else:
                scan_data = {'value': value, 'key': key}
            
            # Scan for hardcoded values
            hardcoded_score = self._scan_hardcoded_values(scan_data, scan_result)
            
            # Scan for string indicators
            string_score = self._scan_string_indicators(scan_data, scan_result)
            
            # Scan for field patterns
            field_score = self._scan_field_patterns(scan_data, scan_result)
            
            # Scan for timestamp issues
            timestamp_score = self._scan_timestamp_issues(scan_data, scan_result)
            
            # Scan for structural issues
            structure_score = self._scan_structural_issues(scan_data, scan_result)
            
            # Calculate overall confidence score
            scan_result['confidence_score'] = (
                hardcoded_score + string_score + field_score + 
                timestamp_score + structure_score
            ) / 5.0
            
            # Determine if fake or suspicious
            if scan_result['confidence_score'] >= 0.8:
                scan_result['is_fake'] = True
                self.purge_statistics['fake_data_detected'] += 1
            elif scan_result['confidence_score'] >= 0.5:
                scan_result['is_suspicious'] = True
            
            return scan_result
            
        except Exception as e:
            logger.error(f"❌ [FAKE-DATA-PURGE] Scan error for {key}: {e}")
            scan_result['is_suspicious'] = True
            scan_result['suspicious_reasons'].append(f"Scan error: {e}")
            return scan_result
    
    def _scan_hardcoded_values(self, data: Dict[str, Any], scan_result: Dict[str, Any]) -> float:
        """Scan for hardcoded placeholder values"""
        score = 0.0
        hardcoded_found = []
        
        try:
            # Check numeric values
            for key, value in data.items():
                if isinstance(value, (int, float, Decimal)):
                    value_float = float(value)
                    
                    # Check against known hardcoded values
                    for category, values in self.fake_patterns['hardcoded_values'].items():
                        if value_float in values:
                            hardcoded_found.append(f"{key}={value_float} (category: {category})")
                            score += 0.3
                    
                    # Check for suspiciously round numbers
                    if value_float > 0 and value_float == int(value_float) and value_float % 1000 == 0:
                        hardcoded_found.append(f"{key}={value_float} (round number)")
                        score += 0.1
            
            if hardcoded_found:
                scan_result['fake_reasons'].extend(hardcoded_found)
                scan_result['scan_details']['hardcoded_values'] = hardcoded_found
            
            return min(score, 1.0)
            
        except Exception as e:
            logger.error(f"❌ [FAKE-DATA-PURGE] Hardcoded value scan error: {e}")
            return 0.0
    
    def _scan_string_indicators(self, data: Dict[str, Any], scan_result: Dict[str, Any]) -> float:
        """Scan for string indicators of fake data"""
        score = 0.0
        indicators_found = []
        
        try:
            # Convert data to string for scanning
            data_str = json.dumps(data, default=str).lower()
            
            # Check for fake indicators
            for indicator in self.fake_patterns['string_indicators']:
                if indicator in data_str:
                    indicators_found.append(indicator)
                    score += 0.2
            
            if indicators_found:
                scan_result['fake_reasons'].extend([f"String indicator: {ind}" for ind in indicators_found])
                scan_result['scan_details']['string_indicators'] = indicators_found
            
            return min(score, 1.0)
            
        except Exception as e:
            logger.error(f"❌ [FAKE-DATA-PURGE] String indicator scan error: {e}")
            return 0.0
    
    def _scan_field_patterns(self, data: Dict[str, Any], scan_result: Dict[str, Any]) -> float:
        """Scan for suspicious field patterns"""
        score = 0.0
        patterns_found = []
        
        try:
            # Check field names
            for key in data.keys():
                key_lower = str(key).lower()
                
                for pattern in self.fake_patterns['field_patterns']:
                    if re.match(pattern, key_lower):
                        patterns_found.append(f"Field pattern: {key} matches {pattern}")
                        score += 0.3
            
            # Check URL patterns in values
            for key, value in data.items():
                if isinstance(value, str):
                    for pattern in self.fake_patterns['url_patterns']:
                        if re.match(pattern, value.lower()):
                            patterns_found.append(f"URL pattern: {value} matches {pattern}")
                            score += 0.4
            
            if patterns_found:
                scan_result['fake_reasons'].extend(patterns_found)
                scan_result['scan_details']['field_patterns'] = patterns_found
            
            return min(score, 1.0)
            
        except Exception as e:
            logger.error(f"❌ [FAKE-DATA-PURGE] Field pattern scan error: {e}")
            return 0.0
    
    def _scan_timestamp_issues(self, data: Dict[str, Any], scan_result: Dict[str, Any]) -> float:
        """Scan for timestamp-related issues"""
        score = 0.0
        timestamp_issues = []
        
        try:
            current_time = time.time()
            
            # Check timestamp fields
            timestamp_fields = ['timestamp', 'time', 'created_at', 'updated_at', 'server_time']
            
            for field in timestamp_fields:
                if field in data:
                    timestamp = data[field]
                    
                    if isinstance(timestamp, (int, float)):
                        # Handle milliseconds
                        if timestamp > 1e12:
                            timestamp = timestamp / 1000
                        
                        # Check for round number timestamps
                        if timestamp in self.fake_patterns['timestamp_patterns']['round_numbers']:
                            timestamp_issues.append(f"Round number timestamp: {timestamp}")
                            score += 0.5
                        
                        # Check age
                        age = current_time - timestamp
                        
                        if age > self.fake_patterns['timestamp_patterns']['too_old_threshold']:
                            timestamp_issues.append(f"Timestamp too old: {age:.1f}s")
                            score += 0.3
                        
                        if age < -self.fake_patterns['timestamp_patterns']['future_threshold']:
                            timestamp_issues.append(f"Timestamp in future: {-age:.1f}s")
                            score += 0.4
            
            if timestamp_issues:
                scan_result['fake_reasons'].extend(timestamp_issues)
                scan_result['scan_details']['timestamp_issues'] = timestamp_issues
            
            return min(score, 1.0)
            
        except Exception as e:
            logger.error(f"❌ [FAKE-DATA-PURGE] Timestamp scan error: {e}")
            return 0.0
    
    def _scan_structural_issues(self, data: Dict[str, Any], scan_result: Dict[str, Any]) -> float:
        """Scan for structural issues that indicate fake data"""
        score = 0.0
        structure_issues = []
        
        try:
            # Check for missing required fields
            missing_fields = []
            for field in self.preservation_rules['required_fields']:
                if field not in data:
                    missing_fields.append(field)
            
            if missing_fields:
                structure_issues.append(f"Missing required fields: {missing_fields}")
                score += 0.2
            
            # Check data source authenticity
            data_source = data.get('data_source', '').lower()
            if data_source and data_source not in self.preservation_rules['authentic_sources']:
                structure_issues.append(f"Non-authentic data source: {data_source}")
                score += 0.3
            
            # Check exchange validity
            exchange = data.get('exchange', '').lower()
            if exchange and exchange not in self.preservation_rules['valid_exchanges']:
                structure_issues.append(f"Invalid exchange: {exchange}")
                score += 0.2
            
            if structure_issues:
                scan_result['suspicious_reasons'].extend(structure_issues)
                scan_result['scan_details']['structure_issues'] = structure_issues
            
            return min(score, 1.0)
            
        except Exception as e:
            logger.error(f"❌ [FAKE-DATA-PURGE] Structure scan error: {e}")
            return 0.0
    
    async def _handle_fake_data(self, key: str, value: Any, scan_result: Dict[str, Any], 
                              purge_session: Dict[str, Any]):
        """Handle identified fake data"""
        try:
            # Log fake data
            fake_data_record = {
                'timestamp': time.time(),
                'key': key,
                'value_hash': hashlib.md5(json.dumps(value, default=str).encode()).hexdigest(),
                'fake_reasons': scan_result['fake_reasons'],
                'confidence_score': scan_result['confidence_score'],
                'purge_session': purge_session['session_id']
            }
            
            # Add to purge session
            purge_session['fake_items_found'].append(fake_data_record)
            purge_session['purged_items'].append(key)
            
            # Update statistics
            self.purge_statistics['data_purged'] += 1
            
            logger.warning(f"🗑️ [FAKE-DATA-PURGE] Purged fake data: {key} - {scan_result['fake_reasons']}")
            
        except Exception as e:
            logger.error(f"❌ [FAKE-DATA-PURGE] Error handling fake data {key}: {e}")
    
    async def _quarantine_suspicious_data(self, key: str, value: Any, scan_result: Dict[str, Any], 
                                        purge_session: Dict[str, Any]):
        """Quarantine suspicious data for further analysis"""
        try:
            quarantine_id = f"quarantine_{int(time.time() * 1000)}_{key}"
            
            quarantine_record = {
                'quarantine_id': quarantine_id,
                'timestamp': time.time(),
                'key': key,
                'value': value,
                'suspicious_reasons': scan_result['suspicious_reasons'],
                'confidence_score': scan_result['confidence_score'],
                'purge_session': purge_session['session_id'],
                'expires_at': time.time() + self.quarantine_duration
            }
            
            # Store in quarantine
            self.quarantined_data[quarantine_id] = quarantine_record
            
            # Update statistics
            self.purge_statistics['data_quarantined'] += 1
            
            logger.warning(f"🔒 [FAKE-DATA-PURGE] Quarantined suspicious data: {key} - {scan_result['suspicious_reasons']}")
            
        except Exception as e:
            logger.error(f"❌ [FAKE-DATA-PURGE] Error quarantining data {key}: {e}")
    
    async def start_continuous_purging(self, data_stores: Dict[str, Dict[str, Any]]):
        """Start continuous fake data purging"""
        if not self.purge_enabled:
            return
        
        logger.info("🔄 [FAKE-DATA-PURGE] Starting continuous fake data purging")
        
        self.monitored_data_stores = data_stores
        
        # Start purge monitoring task
        purge_task = asyncio.create_task(self._continuous_purge_monitor())
        
        # Start quarantine cleanup task
        cleanup_task = asyncio.create_task(self._cleanup_quarantine())
        
        return purge_task, cleanup_task
    
    async def _continuous_purge_monitor(self):
        """Continuously monitor and purge fake data"""
        while self.purge_enabled:
            try:
                for store_name, data_store in self.monitored_data_stores.items():
                    if data_store:  # Only scan non-empty stores
                        cleaned_data, purge_report = await self.scan_and_purge_fake_data(data_store, store_name)
                        
                        # Update the original data store
                        data_store.clear()
                        data_store.update(cleaned_data)
                
                await asyncio.sleep(self.scan_interval)
                
            except Exception as e:
                logger.error(f"❌ [FAKE-DATA-PURGE] Continuous purge error: {e}")
                await asyncio.sleep(10)
    
    async def _cleanup_quarantine(self):
        """Clean up expired quarantined data"""
        while self.purge_enabled:
            try:
                current_time = time.time()
                expired_items = []
                
                for quarantine_id, record in self.quarantined_data.items():
                    if current_time > record['expires_at']:
                        expired_items.append(quarantine_id)
                
                # Remove expired items
                for quarantine_id in expired_items:
                    del self.quarantined_data[quarantine_id]
                    logger.debug(f"🧹 [FAKE-DATA-PURGE] Cleaned up expired quarantine: {quarantine_id}")
                
                await asyncio.sleep(60)  # Check every minute
                
            except Exception as e:
                logger.error(f"❌ [FAKE-DATA-PURGE] Quarantine cleanup error: {e}")
                await asyncio.sleep(60)
    
    def get_purge_statistics(self) -> Dict[str, Any]:
        """Get comprehensive purge statistics"""
        current_time = time.time()
        
        return {
            **self.purge_statistics,
            'purge_enabled': self.purge_enabled,
            'aggressive_purging': self.aggressive_purging,
            'quarantined_items_count': len(self.quarantined_data),
            'recent_purges': len([p for p in self.purged_data_log if current_time - p['start_time'] < 3600]),
            'purge_success_rate': (self.purge_statistics['data_purged'] / 
                                 max(self.purge_statistics['fake_data_detected'], 1)) * 100,
            'monitored_stores': list(self.monitored_data_stores.keys()) if self.monitored_data_stores else []
        }
    
    def get_quarantined_data(self) -> Dict[str, Any]:
        """Get currently quarantined data"""
        return {qid: {k: v for k, v in record.items() if k != 'value'} 
                for qid, record in self.quarantined_data.items()}
    
    async def release_quarantined_data(self, quarantine_id: str) -> Optional[Any]:
        """Release data from quarantine (if determined to be real)"""
        if quarantine_id in self.quarantined_data:
            record = self.quarantined_data[quarantine_id]
            del self.quarantined_data[quarantine_id]
            
            self.purge_statistics['false_positives'] += 1
            logger.info(f"🔓 [FAKE-DATA-PURGE] Released quarantined data: {quarantine_id}")
            
            return record['value']
        
        return None
