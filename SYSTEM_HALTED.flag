{"halt_time": 1751376261.7866302, "reason": "Data validation failed", "error_details": {"error": "CRITICAL: Market data validation failed - Market data missing price information", "error_type": "RuntimeError", "component": "master_validator", "context": {"source_type": "market_data", "validation_active": true, "system_wide_validation": true}, "timestamp": 1751376261.7768123, "traceback": "Traceback (most recent call last):\n  File \"X:\\autogpt_trade_project\\The_real_deal\\autogpt-trader\\src\\validation\\real_data_validator.py\", line 142, in validate_market_data\n    raise ValueError(f\"Market data missing price information\")\nValueError: Market data missing price information\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"X:\\autogpt_trade_project\\The_real_deal\\autogpt-trader\\src\\validation\\master_real_data_validator.py\", line 100, in validate_system_wide_data_integrity\n    is_valid, details = await self.validation_pipeline[source_type](source_data)\n                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"X:\\autogpt_trade_project\\The_real_deal\\autogpt-trader\\src\\validation\\master_real_data_validator.py\", line 256, in _validate_market_data_pipeline\n    market_valid = self.real_data_validator.validate_market_data(data, symbol)\n  File \"X:\\autogpt_trade_project\\The_real_deal\\autogpt-trader\\src\\validation\\real_data_validator.py\", line 154, in validate_market_data\n    raise RuntimeError(f\"CRITICAL: Market data validation failed - {e}\")\nRuntimeError: CRITICAL: Market data validation failed - Market data missing price information\n", "category": "validation_failures", "is_critical": true}}