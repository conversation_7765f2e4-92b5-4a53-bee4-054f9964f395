"""
Real Data Validator - Comprehensive validation system for ensuring ONLY real data is used
"""

import time
import logging
import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Union
from decimal import Decimal
import re
import json
import hashlib

logger = logging.getLogger(__name__)

class RealDataValidator:
    """
    Comprehensive validator to ensure ONLY real, live data is used throughout the system.
    Zero tolerance for any simulated, mock, or hardcoded data.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.validation_enabled = True
        self.fail_fast_mode = True
        self.max_data_age_seconds = 30  # Data must be fresher than 30 seconds
        self.balance_freshness_seconds = 10  # Balance data must be fresher than 10 seconds
        
        # Prohibited patterns that indicate fake/test data
        self.prohibited_patterns = {
            'fake_prices': [50000.0, 3000.0, 100.0, 0.5, 300.0, 0.6, 0.1],  # Hardcoded fallback prices
            'test_balances': [10000.0, 1000.0, 100.0],  # Common test balance amounts
            'simulation_keywords': ['test', 'mock', 'fake', 'simulation', 'demo', 'sandbox'],
            'hardcoded_symbols': ['BTC-USD', 'ETH-USD', 'SOL-USD'],  # Hardcoded trading pairs
        }
        
        # Track validation history
        self.validation_history = []
        self.last_validation_time = 0
        self.validation_interval = 30  # Validate every 30 seconds
        
        logger.info("🔒 [REAL-DATA-VALIDATOR] Initialized with zero tolerance for fake data")
    
    def validate_api_response_authenticity(self, response: Dict[str, Any], source: str) -> bool:
        """
        Validate that API response contains authentic, live data
        """
        if not self.validation_enabled:
            return True
            
        try:
            # Check for timestamp freshness
            timestamp = self._extract_timestamp(response)
            if not self._is_timestamp_fresh(timestamp, self.max_data_age_seconds):
                raise ValueError(f"API response from {source} contains stale data (timestamp: {timestamp})")
            
            # Check for prohibited patterns
            response_str = json.dumps(response, default=str).lower()
            for keyword in self.prohibited_patterns['simulation_keywords']:
                if keyword in response_str:
                    raise ValueError(f"API response from {source} contains prohibited keyword: {keyword}")
            
            # Validate data structure authenticity
            if not self._validate_data_structure(response, source):
                raise ValueError(f"API response from {source} has invalid structure")
            
            # Check for hardcoded values
            if self._contains_hardcoded_values(response):
                raise ValueError(f"API response from {source} contains hardcoded values")
            
            logger.debug(f"✅ [REAL-DATA-VALIDATOR] API response from {source} validated as authentic")
            return True
            
        except Exception as e:
            if self.fail_fast_mode:
                logger.error(f"❌ [REAL-DATA-VALIDATOR] API validation failed for {source}: {e}")
                raise RuntimeError(f"CRITICAL: Real data validation failed - {e}")
            else:
                logger.warning(f"⚠️ [REAL-DATA-VALIDATOR] API validation warning for {source}: {e}")
                return False
    
    def validate_balance_data(self, balance_data: Dict[str, Any], exchange: str) -> bool:
        """
        Validate balance data is live and authentic
        """
        if not self.validation_enabled:
            return True
            
        try:
            # Check timestamp freshness (stricter for balance data)
            timestamp = self._extract_timestamp(balance_data)
            if not self._is_timestamp_fresh(timestamp, self.balance_freshness_seconds):
                raise ValueError(f"Balance data from {exchange} is stale (timestamp: {timestamp})")
            
            # Check for test balance amounts
            for currency, amount in balance_data.get('balances', {}).items():
                if isinstance(amount, (int, float, Decimal)):
                    amount_float = float(amount)
                    if amount_float in self.prohibited_patterns['test_balances']:
                        raise ValueError(f"Balance data contains test amount: {amount_float}")
            
            # Validate balance structure
            required_fields = ['balances', 'timestamp']
            for field in required_fields:
                if field not in balance_data:
                    raise ValueError(f"Balance data missing required field: {field}")
            
            logger.debug(f"✅ [REAL-DATA-VALIDATOR] Balance data from {exchange} validated")
            return True
            
        except Exception as e:
            if self.fail_fast_mode:
                logger.error(f"❌ [REAL-DATA-VALIDATOR] Balance validation failed for {exchange}: {e}")
                raise RuntimeError(f"CRITICAL: Balance data validation failed - {e}")
            else:
                logger.warning(f"⚠️ [REAL-DATA-VALIDATOR] Balance validation warning for {exchange}: {e}")
                return False
    
    def validate_market_data(self, market_data: Dict[str, Any], symbol: str) -> bool:
        """
        Validate market data is live and not hardcoded
        """
        if not self.validation_enabled:
            return True
            
        try:
            # Check timestamp freshness
            timestamp = self._extract_timestamp(market_data)
            if not self._is_timestamp_fresh(timestamp, self.max_data_age_seconds):
                raise ValueError(f"Market data for {symbol} is stale (timestamp: {timestamp})")
            
            # Check for hardcoded prices
            price = market_data.get('price') or market_data.get('last') or market_data.get('close')
            if price and float(price) in self.prohibited_patterns['fake_prices']:
                raise ValueError(f"Market data contains hardcoded price: {price}")
            
            # Validate market data structure
            required_fields = ['price', 'timestamp']
            for field in required_fields:
                if field not in market_data and not any(alt in market_data for alt in ['last', 'close']):
                    raise ValueError(f"Market data missing price information")
            
            # Check for realistic price movements
            if not self._validate_price_realism(market_data, symbol):
                raise ValueError(f"Market data for {symbol} shows unrealistic price patterns")
            
            logger.debug(f"✅ [REAL-DATA-VALIDATOR] Market data for {symbol} validated")
            return True
            
        except Exception as e:
            if self.fail_fast_mode:
                logger.error(f"❌ [REAL-DATA-VALIDATOR] Market data validation failed for {symbol}: {e}")
                raise RuntimeError(f"CRITICAL: Market data validation failed - {e}")
            else:
                logger.warning(f"⚠️ [REAL-DATA-VALIDATOR] Market data validation warning for {symbol}: {e}")
                return False
    
    def validate_trading_signal(self, signal: Dict[str, Any]) -> bool:
        """
        Validate trading signal is based on real data
        """
        if not self.validation_enabled:
            return True
            
        try:
            # Check signal timestamp
            timestamp = self._extract_timestamp(signal)
            if not self._is_timestamp_fresh(timestamp, self.max_data_age_seconds):
                raise ValueError(f"Trading signal is stale (timestamp: {timestamp})")
            
            # Validate signal has data source traceability
            if 'data_sources' not in signal:
                raise ValueError("Trading signal missing data source traceability")
            
            # Check confidence level is realistic
            confidence = signal.get('confidence', 0)
            if confidence <= 0 or confidence > 1:
                raise ValueError(f"Trading signal has invalid confidence: {confidence}")
            
            # Validate signal is not from prohibited sources
            data_sources = signal.get('data_sources', [])
            for source in data_sources:
                if any(keyword in str(source).lower() for keyword in self.prohibited_patterns['simulation_keywords']):
                    raise ValueError(f"Trading signal from prohibited source: {source}")
            
            logger.debug(f"✅ [REAL-DATA-VALIDATOR] Trading signal validated")
            return True
            
        except Exception as e:
            if self.fail_fast_mode:
                logger.error(f"❌ [REAL-DATA-VALIDATOR] Trading signal validation failed: {e}")
                raise RuntimeError(f"CRITICAL: Trading signal validation failed - {e}")
            else:
                logger.warning(f"⚠️ [REAL-DATA-VALIDATOR] Trading signal validation warning: {e}")
                return False
    
    def validate_neural_input_data(self, input_data: Dict[str, Any]) -> bool:
        """
        Validate neural network input data is from real sources
        """
        if not self.validation_enabled:
            return True
            
        try:
            # Check data freshness
            timestamp = self._extract_timestamp(input_data)
            if not self._is_timestamp_fresh(timestamp, self.max_data_age_seconds):
                raise ValueError(f"Neural input data is stale (timestamp: {timestamp})")
            
            # Validate data sources
            if 'features' in input_data:
                for feature_name, feature_value in input_data['features'].items():
                    if self._is_synthetic_feature(feature_name, feature_value):
                        raise ValueError(f"Neural input contains synthetic feature: {feature_name}")
            
            # Check for training data authenticity
            if 'training_data' in input_data:
                if not self._validate_training_data_authenticity(input_data['training_data']):
                    raise ValueError("Neural input contains non-authentic training data")
            
            logger.debug(f"✅ [REAL-DATA-VALIDATOR] Neural input data validated")
            return True
            
        except Exception as e:
            if self.fail_fast_mode:
                logger.error(f"❌ [REAL-DATA-VALIDATOR] Neural input validation failed: {e}")
                raise RuntimeError(f"CRITICAL: Neural input validation failed - {e}")
            else:
                logger.warning(f"⚠️ [REAL-DATA-VALIDATOR] Neural input validation warning: {e}")
                return False
    
    def validate_order_execution_data(self, order_data: Dict[str, Any], exchange: str) -> bool:
        """
        Validate order execution data is from real exchange
        """
        if not self.validation_enabled:
            return True
            
        try:
            # Check for real transaction ID
            tx_id = order_data.get('transaction_id') or order_data.get('order_id') or order_data.get('id')
            if not tx_id or self._is_fake_transaction_id(tx_id):
                raise ValueError(f"Order execution missing or has fake transaction ID: {tx_id}")
            
            # Validate execution timestamp
            timestamp = self._extract_timestamp(order_data)
            if not self._is_timestamp_fresh(timestamp, 60):  # Orders can be up to 1 minute old
                raise ValueError(f"Order execution timestamp is stale: {timestamp}")
            
            # Check for real exchange response structure
            if not self._validate_exchange_response_structure(order_data, exchange):
                raise ValueError(f"Order execution data has invalid structure for {exchange}")
            
            # Validate order amounts are not test values
            amount = order_data.get('amount') or order_data.get('quantity')
            if amount and float(amount) in self.prohibited_patterns['test_balances']:
                raise ValueError(f"Order execution contains test amount: {amount}")
            
            logger.debug(f"✅ [REAL-DATA-VALIDATOR] Order execution data validated for {exchange}")
            return True
            
        except Exception as e:
            if self.fail_fast_mode:
                logger.error(f"❌ [REAL-DATA-VALIDATOR] Order execution validation failed for {exchange}: {e}")
                raise RuntimeError(f"CRITICAL: Order execution validation failed - {e}")
            else:
                logger.warning(f"⚠️ [REAL-DATA-VALIDATOR] Order execution validation warning for {exchange}: {e}")
                return False
    
    def _extract_timestamp(self, data: Dict[str, Any]) -> Optional[float]:
        """Extract timestamp from data structure"""
        timestamp_fields = ['timestamp', 'time', 'created_at', 'updated_at', 'server_time']
        
        for field in timestamp_fields:
            if field in data:
                timestamp = data[field]
                if isinstance(timestamp, (int, float)):
                    return float(timestamp)
                elif isinstance(timestamp, str):
                    try:
                        # Try parsing ISO format
                        dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                        return dt.timestamp()
                    except:
                        try:
                            # Try parsing as timestamp string
                            return float(timestamp)
                        except:
                            continue
        
        # If no timestamp found, use current time but log warning
        logger.warning("⚠️ [REAL-DATA-VALIDATOR] No timestamp found in data, using current time")
        return time.time()
    
    def _is_timestamp_fresh(self, timestamp: Optional[float], max_age_seconds: int) -> bool:
        """Check if timestamp is fresh enough"""
        if timestamp is None:
            return False

        current_time = time.time()
        age_seconds = current_time - timestamp

        return age_seconds <= max_age_seconds

    def _validate_data_structure(self, data: Dict[str, Any], source: str) -> bool:
        """Validate data structure is consistent with real API responses"""
        # Basic structure validation
        if not isinstance(data, dict):
            return False

        # Check for minimum required fields based on source
        if source.lower() in ['bybit', 'coinbase']:
            # Exchange APIs should have certain fields
            if 'timestamp' not in data and 'time' not in data:
                logger.warning(f"⚠️ [REAL-DATA-VALIDATOR] Missing timestamp in {source} response")

        return True

    def _contains_hardcoded_values(self, data: Dict[str, Any]) -> bool:
        """Check if data contains hardcoded values"""
        data_str = json.dumps(data, default=str)

        # Check for exact hardcoded prices
        for price in self.prohibited_patterns['fake_prices']:
            if str(price) in data_str:
                return True

        return False

    def _validate_price_realism(self, market_data: Dict[str, Any], symbol: str) -> bool:
        """Validate price data shows realistic market behavior"""
        price = market_data.get('price') or market_data.get('last') or market_data.get('close')
        if not price:
            return False

        price_float = float(price)

        # Check for obviously fake prices (too round numbers)
        if price_float in [1.0, 10.0, 100.0, 1000.0, 10000.0]:
            logger.warning(f"⚠️ [REAL-DATA-VALIDATOR] Suspicious round price for {symbol}: {price_float}")
            return False

        # Check for reasonable price ranges based on symbol
        if 'BTC' in symbol.upper():
            if price_float < 1000 or price_float > 200000:
                logger.warning(f"⚠️ [REAL-DATA-VALIDATOR] Unrealistic BTC price: {price_float}")
                return False

        return True

    def _is_synthetic_feature(self, feature_name: str, feature_value: Any) -> bool:
        """Check if a feature appears to be synthetic/generated"""
        feature_name_lower = feature_name.lower()

        # Check for synthetic feature names
        synthetic_indicators = ['mock', 'fake', 'test', 'synthetic', 'generated', 'dummy']
        if any(indicator in feature_name_lower for indicator in synthetic_indicators):
            return True

        # Check for obviously fake values
        if isinstance(feature_value, (int, float)):
            if feature_value in [0.0, 1.0, -1.0, 999.0, 9999.0]:
                return True

        return False

    def _validate_training_data_authenticity(self, training_data: Any) -> bool:
        """Validate training data is from real market sources"""
        if isinstance(training_data, dict):
            if 'source' in training_data:
                source = str(training_data['source']).lower()
                if any(keyword in source for keyword in self.prohibited_patterns['simulation_keywords']):
                    return False

        return True

    def _is_fake_transaction_id(self, tx_id: str) -> bool:
        """Check if transaction ID appears to be fake"""
        tx_id_str = str(tx_id).lower()

        # Check for obvious fake IDs
        fake_patterns = ['mock', 'fake', 'test', '12345', 'abcdef', 'dummy']
        if any(pattern in tx_id_str for pattern in fake_patterns):
            return True

        # Check for too simple patterns
        if len(tx_id_str) < 8:  # Real transaction IDs are usually longer
            return True

        return False

    def _validate_exchange_response_structure(self, order_data: Dict[str, Any], exchange: str) -> bool:
        """Validate order response structure matches real exchange format"""
        exchange_lower = exchange.lower()

        if exchange_lower == 'bybit':
            # Bybit should have specific fields
            required_fields = ['orderId', 'symbol']
            return any(field in order_data for field in required_fields)
        elif exchange_lower == 'coinbase':
            # Coinbase should have specific fields
            required_fields = ['id', 'product_id']
            return any(field in order_data for field in required_fields)

        return True  # Unknown exchange, assume valid

    async def perform_comprehensive_validation(self, data_sources: Dict[str, Any]) -> Dict[str, bool]:
        """
        Perform comprehensive validation across all data sources
        """
        validation_results = {}

        try:
            # Validate API responses
            if 'api_responses' in data_sources:
                for source, response in data_sources['api_responses'].items():
                    validation_results[f'api_{source}'] = self.validate_api_response_authenticity(response, source)

            # Validate balance data
            if 'balance_data' in data_sources:
                for exchange, balance in data_sources['balance_data'].items():
                    validation_results[f'balance_{exchange}'] = self.validate_balance_data(balance, exchange)

            # Validate market data
            if 'market_data' in data_sources:
                for symbol, data in data_sources['market_data'].items():
                    validation_results[f'market_{symbol}'] = self.validate_market_data(data, symbol)

            # Validate trading signals
            if 'trading_signals' in data_sources:
                for i, signal in enumerate(data_sources['trading_signals']):
                    validation_results[f'signal_{i}'] = self.validate_trading_signal(signal)

            # Validate neural inputs
            if 'neural_inputs' in data_sources:
                for model, input_data in data_sources['neural_inputs'].items():
                    validation_results[f'neural_{model}'] = self.validate_neural_input_data(input_data)

            # Check overall validation status
            all_valid = all(validation_results.values())
            validation_results['overall_status'] = all_valid

            if all_valid:
                logger.info("✅ [REAL-DATA-VALIDATOR] Comprehensive validation PASSED - all data sources authentic")
            else:
                failed_validations = [k for k, v in validation_results.items() if not v]
                logger.error(f"❌ [REAL-DATA-VALIDATOR] Comprehensive validation FAILED - failed: {failed_validations}")

                if self.fail_fast_mode:
                    raise RuntimeError(f"CRITICAL: Real data validation failed for: {failed_validations}")

            return validation_results

        except Exception as e:
            logger.error(f"❌ [REAL-DATA-VALIDATOR] Comprehensive validation error: {e}")
            if self.fail_fast_mode:
                raise
            return {'overall_status': False, 'error': str(e)}

    def get_validation_report(self) -> Dict[str, Any]:
        """Get comprehensive validation report"""
        return {
            'validator_status': 'ACTIVE' if self.validation_enabled else 'DISABLED',
            'fail_fast_mode': self.fail_fast_mode,
            'max_data_age_seconds': self.max_data_age_seconds,
            'balance_freshness_seconds': self.balance_freshness_seconds,
            'validation_history_count': len(self.validation_history),
            'last_validation_time': self.last_validation_time,
            'prohibited_patterns': self.prohibited_patterns
        }
