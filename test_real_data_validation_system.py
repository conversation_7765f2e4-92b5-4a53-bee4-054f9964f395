#!/usr/bin/env python3
"""
Comprehensive Test Suite for Real Data Validation System
Tests all validation mechanisms with live data and verifies fail-fast behavior
"""

import asyncio
import time
import logging
import sys
import json
from datetime import datetime, timedelta
from decimal import Decimal
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Import validation components
from src.validation.master_real_data_validator import MasterRealDataValidator
from src.validation.real_data_validator import RealDataValidator
from src.validation.api_data_validator import APIDataValidator
from src.validation.trading_execution_validator import TradingExecutionValidator
from src.validation.balance_verification_system import BalanceVerificationSystem
from src.validation.market_data_integrity_validator import MarketDataIntegrityValidator
from src.validation.neural_learning_data_validator import NeuralLearningDataValidator
from src.validation.fail_fast_error_handler import FailFastError<PERSON><PERSON><PERSON>
from src.validation.runtime_validation_checkpoints import RuntimeValidationCheckpoints

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - [%(levelname)s] - %(name)s - %(message)s'
)
logger = logging.getLogger(__name__)

class RealDataValidationTester:
    """Comprehensive tester for real data validation system"""
    
    def __init__(self):
        self.test_results = {}
        try:
            self.master_validator = MasterRealDataValidator()
            logger.info("✅ [TEST-INIT] Master validator initialized successfully")
        except Exception as e:
            logger.error(f"❌ [TEST-INIT] Failed to initialize master validator: {e}")
            self.master_validator = None
        self.test_count = 0
        self.passed_tests = 0
        self.failed_tests = 0
        
    async def run_comprehensive_tests(self):
        """Run all validation tests"""
        logger.info("🧪 [VALIDATION-TESTS] Starting comprehensive real data validation tests")
        
        test_suite = [
            ("API Data Validation", self.test_api_data_validation),
            ("Balance Verification", self.test_balance_verification),
            ("Market Data Integrity", self.test_market_data_integrity),
            ("Trading Execution Validation", self.test_trading_execution_validation),
            ("Neural Learning Data Validation", self.test_neural_learning_validation),
            ("Fail-Fast Error Handling", self.test_fail_fast_mechanisms),
            ("Runtime Validation Checkpoints", self.test_runtime_checkpoints),
            ("Master Validator Integration", self.test_master_validator_integration),
            ("Fake Data Detection", self.test_fake_data_detection),
            ("System-Wide Validation", self.test_system_wide_validation)
        ]
        
        for test_name, test_func in test_suite:
            try:
                logger.info(f"🔍 [TEST] Running: {test_name}")
                result = await test_func()
                self.test_results[test_name] = result
                
                if result['passed']:
                    self.passed_tests += 1
                    logger.info(f"✅ [PASSED] {test_name}")
                else:
                    self.failed_tests += 1
                    logger.error(f"❌ [FAILED] {test_name}: {result.get('error', 'Unknown error')}")
                
                self.test_count += 1
                
            except Exception as e:
                self.failed_tests += 1
                self.test_count += 1
                self.test_results[test_name] = {
                    'passed': False,
                    'error': str(e),
                    'exception': True
                }
                logger.error(f"❌ [EXCEPTION] {test_name}: {e}")
        
        # Generate final report
        await self.generate_test_report()
    
    async def test_api_data_validation(self):
        """Test API data validation with real and fake data"""
        try:
            api_validator = APIDataValidator()
            
            # Test 1: Valid API response
            valid_response = {
                'timestamp': time.time(),
                'data': {'price': 50123.45, 'volume': 1234567},
                'status': 'success'
            }
            
            valid_result, valid_details = await api_validator.validate_api_response(
                valid_response, 'bybit', 'https://api.bybit.com/v5/market/tickers', time.time()
            )
            
            if not valid_result:
                return {'passed': False, 'error': 'Valid API response failed validation'}
            
            # Test 2: Fake API response (should fail)
            fake_response = {
                'timestamp': time.time() - 100,  # Too old
                'data': {'price': 50000.0, 'volume': 1000000},  # Hardcoded values
                'mock_data': True
            }
            
            try:
                fake_result, fake_details = await api_validator.validate_api_response(
                    fake_response, 'test_exchange', 'https://test.api.com', time.time()
                )
                
                if fake_result:
                    return {'passed': False, 'error': 'Fake API response passed validation (should fail)'}
            except RuntimeError:
                # Expected to fail with RuntimeError in fail-fast mode
                pass
            
            return {'passed': True, 'details': 'API validation working correctly'}
            
        except Exception as e:
            return {'passed': False, 'error': str(e)}
    
    async def test_balance_verification(self):
        """Test balance verification with real and fake balance data"""
        try:
            balance_verifier = BalanceVerificationSystem()
            
            # Test 1: Valid balance data
            valid_balance = {
                'timestamp': time.time(),
                'balances': {
                    'BTC': Decimal('0.00123456'),
                    'USDT': Decimal('234.56789'),
                    'ETH': Decimal('1.23456789')
                }
            }
            
            valid_result, valid_details = await balance_verifier.verify_balance_authenticity(
                valid_balance, 'bybit', time.time()
            )
            
            if not valid_result:
                return {'passed': False, 'error': 'Valid balance failed verification'}
            
            # Test 2: Fake balance data (should fail)
            fake_balance = {
                'timestamp': time.time() - 20,  # Too old for balance data
                'balances': {
                    'BTC': 1000.0,  # Prohibited test amount
                    'USDT': 10000.0  # Prohibited test amount
                }
            }
            
            try:
                fake_result, fake_details = await balance_verifier.verify_balance_authenticity(
                    fake_balance, 'test_exchange', time.time()
                )
                
                if fake_result:
                    return {'passed': False, 'error': 'Fake balance passed verification (should fail)'}
            except RuntimeError:
                # Expected to fail
                pass
            
            return {'passed': True, 'details': 'Balance verification working correctly'}
            
        except Exception as e:
            return {'passed': False, 'error': str(e)}
    
    async def test_market_data_integrity(self):
        """Test market data integrity validation"""
        try:
            market_validator = MarketDataIntegrityValidator()
            
            # Test 1: Valid technical indicator data
            valid_indicator = {
                'timestamp': time.time(),
                'values': [50123.45, 50234.56, 50345.67, 50456.78, 50567.89],
                'data_source': 'live_api',
                'symbol': 'BTCUSDT'
            }
            
            valid_result, valid_details = await market_validator.validate_technical_indicator_data(
                valid_indicator, 'sma', 'BTCUSDT'
            )
            
            if not valid_result:
                return {'passed': False, 'error': 'Valid indicator data failed validation'}
            
            # Test 2: Synthetic indicator data (should fail)
            synthetic_indicator = {
                'timestamp': time.time() - 100,  # Too old
                'values': [1.0, 1.0, 1.0, 1.0, 1.0],  # Constant fake values
                'data_source': 'synthetic_generator',
                'symbol': 'TESTUSDT'
            }
            
            try:
                synthetic_result, synthetic_details = await market_validator.validate_technical_indicator_data(
                    synthetic_indicator, 'sma', 'TESTUSDT'
                )
                
                if synthetic_result:
                    return {'passed': False, 'error': 'Synthetic indicator passed validation (should fail)'}
            except RuntimeError:
                # Expected to fail
                pass
            
            return {'passed': True, 'details': 'Market data integrity validation working correctly'}
            
        except Exception as e:
            return {'passed': False, 'error': str(e)}
    
    async def test_trading_execution_validation(self):
        """Test trading execution validation"""
        try:
            execution_validator = TradingExecutionValidator()
            
            # Test 1: Valid order execution
            valid_order = {
                'orderId': 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
                'symbol': 'BTCUSDT',
                'side': 'BUY',
                'orderType': 'LIMIT',
                'qty': '0.001',
                'price': '50123.45',
                'orderStatus': 'FILLED',
                'timestamp': time.time() * 1000  # Milliseconds
            }
            
            valid_result, valid_details = await execution_validator.validate_order_execution(
                valid_order, 'bybit', time.time()
            )
            
            if not valid_result:
                return {'passed': False, 'error': 'Valid order execution failed validation'}
            
            # Test 2: Fake order execution (should fail)
            fake_order = {
                'orderId': 'fake12345',  # Fake transaction ID
                'symbol': 'TESTUSDT',
                'side': 'BUY',
                'orderType': 'MARKET',
                'qty': '100.0',  # Test amount
                'mock_execution': True,
                'timestamp': time.time() - 120  # Too old
            }
            
            try:
                fake_result, fake_details = await execution_validator.validate_order_execution(
                    fake_order, 'test_exchange', time.time()
                )
                
                if fake_result:
                    return {'passed': False, 'error': 'Fake order execution passed validation (should fail)'}
            except RuntimeError:
                # Expected to fail
                pass
            
            return {'passed': True, 'details': 'Trading execution validation working correctly'}
            
        except Exception as e:
            return {'passed': False, 'error': str(e)}
    
    async def test_neural_learning_validation(self):
        """Test neural learning data validation"""
        try:
            neural_validator = NeuralLearningDataValidator()
            
            # Test 1: Valid training data
            valid_training = {
                'timestamp': time.time(),
                'authenticity': {'is_real_data': True, 'data_source': 'live_trading'},
                'outcomes': [
                    {'transaction_id': 'real_tx_123', 'pnl': 12.34, 'symbol': 'BTCUSDT'},
                    {'transaction_id': 'real_tx_456', 'pnl': -5.67, 'symbol': 'ETHUSDT'}
                ],
                'features': {
                    'price_momentum': [0.123, 0.234, 0.345],
                    'volume_profile': [1234567, 2345678, 3456789]
                }
            }
            
            valid_result, valid_details = await neural_validator.validate_training_data(
                valid_training, 'profit_predictor', 'trading_outcomes'
            )
            
            if not valid_result:
                return {'passed': False, 'error': 'Valid training data failed validation'}
            
            # Test 2: Synthetic training data (should fail)
            synthetic_training = {
                'timestamp': time.time(),
                'authenticity': {'is_real_data': False, 'data_source': 'synthetic_generator'},
                'outcomes': [
                    {'simulated_profit': 100.0, 'mock_result': True},
                    {'generated_return': -50.0, 'test_outcome': True}
                ],
                'features': {
                    'synthetic_momentum': [1.0, 1.0, 1.0],
                    'fake_volume': [1000000, 1000000, 1000000]
                }
            }
            
            try:
                synthetic_result, synthetic_details = await neural_validator.validate_training_data(
                    synthetic_training, 'test_model', 'synthetic_outcomes'
                )
                
                if synthetic_result:
                    return {'passed': False, 'error': 'Synthetic training data passed validation (should fail)'}
            except RuntimeError:
                # Expected to fail
                pass
            
            return {'passed': True, 'details': 'Neural learning validation working correctly'}
            
        except Exception as e:
            return {'passed': False, 'error': str(e)}
    
    async def test_fail_fast_mechanisms(self):
        """Test fail-fast error handling mechanisms"""
        try:
            error_handler = FailFastErrorHandler()
            
            # Test 1: Non-critical error (should continue)
            non_critical_error = ValueError("Minor calculation error")
            context = {'trading_active': False, 'component': 'data_processor', 'test_mode': True}

            should_continue = await error_handler.handle_error(non_critical_error, context, 'test_component')
            
            if not should_continue:
                return {'passed': False, 'error': 'Non-critical error caused system halt'}
            
            # Test 2: Critical error (should halt)
            critical_error = RuntimeError("CRITICAL: Real data validation failed")
            critical_context = {'trading_active': True, 'live_trading': True, 'test_mode': False}

            try:
                should_continue = await error_handler.handle_error(critical_error, critical_context, 'validator')
                
                if should_continue:
                    return {'passed': False, 'error': 'Critical error did not halt system'}
            except RuntimeError:
                # Expected behavior in fail-fast mode
                pass
            
            # Test 3: Placeholder detection
            placeholder_valid = error_handler.validate_no_placeholder_substitution(50000.0, 'prices', 'test_price')
            
            if placeholder_valid:
                return {'passed': False, 'error': 'Placeholder value passed validation (should fail)'}
            
            return {'passed': True, 'details': 'Fail-fast mechanisms working correctly'}
            
        except Exception as e:
            return {'passed': False, 'error': str(e)}
    
    async def test_runtime_checkpoints(self):
        """Test runtime validation checkpoints"""
        try:
            checkpoints = RuntimeValidationCheckpoints()
            
            # Test 1: Data authenticity assertion
            valid_data = {
                'timestamp': time.time(),
                'price': 50123.45,
                'volume': 1234567,
                'data_source': 'live_api'
            }
            
            # Should not raise exception
            checkpoints.assert_data_authenticity(valid_data, 'test_valid_data')
            
            # Test 2: Fake data assertion (should fail)
            fake_data = {
                'timestamp': time.time() - 100,  # Too old
                'price': 50000.0,  # Placeholder value
                'mock_data': True
            }
            
            try:
                checkpoints.assert_data_authenticity(fake_data, 'test_fake_data')
                return {'passed': False, 'error': 'Fake data passed authenticity assertion (should fail)'}
            except AssertionError:
                # Expected to fail
                pass
            
            return {'passed': True, 'details': 'Runtime checkpoints working correctly'}
            
        except Exception as e:
            return {'passed': False, 'error': str(e)}
    
    async def test_master_validator_integration(self):
        """Test master validator integration"""
        try:
            # Test system-wide validation with mixed data
            test_data_sources = {
                'api_responses': {
                    'bybit': {
                        'timestamp': time.time(),
                        'data': {'price': 50123.45, 'volume': 1234567},
                        'status': 'success'
                    }
                },
                'balance_data': {
                    'bybit': {
                        'timestamp': time.time(),
                        'balances': {'BTC': Decimal('0.00123456'), 'USDT': Decimal('234.56')}
                    }
                },
                'market_data': {
                    'BTCUSDT': {
                        'timestamp': time.time(),
                        'price': 50123.45,
                        'volume': 1234567
                    }
                }
            }

            # Check if master validator is properly initialized
            if not hasattr(self.master_validator, 'validate_system_wide_data_integrity'):
                return {'passed': False, 'error': 'Master validator not properly initialized'}

            all_valid, validation_report = await self.master_validator.validate_system_wide_data_integrity(test_data_sources)

            if validation_report is None:
                return {'passed': False, 'error': 'Validation report is None - initialization issue'}

            if not all_valid:
                return {'passed': False, 'error': f'System-wide validation failed: {validation_report}'}

            return {'passed': True, 'details': 'Master validator integration working correctly'}

        except Exception as e:
            return {'passed': False, 'error': str(e)}
    
    async def test_fake_data_detection(self):
        """Test comprehensive fake data detection"""
        try:
            real_validator = RealDataValidator()
            
            # Test various fake data patterns
            fake_patterns = [
                {'mock_price': 50000.0, 'test_mode': True},
                {'simulation_data': True, 'fake_volume': 1000000},
                {'demo_account': True, 'placeholder_balance': 10000.0},
                {'synthetic_features': [1.0, 1.0, 1.0], 'generated_data': True}
            ]
            
            for i, fake_data in enumerate(fake_patterns):
                try:
                    valid = real_validator.validate_api_response_authenticity(fake_data, f'test_source_{i}')
                    if valid:
                        return {'passed': False, 'error': f'Fake data pattern {i} passed validation (should fail)'}
                except RuntimeError:
                    # Expected to fail
                    continue
            
            return {'passed': True, 'details': 'Fake data detection working correctly'}
            
        except Exception as e:
            return {'passed': False, 'error': str(e)}
    
    async def test_system_wide_validation(self):
        """Test complete system-wide validation with comprehensive data"""
        try:
            # Check if master validator is available
            if self.master_validator is None:
                return {'passed': False, 'error': 'Master validator not initialized'}

            # Create comprehensive test data
            comprehensive_data = {
                'api_responses': {
                    'bybit': {
                        'timestamp': time.time(),
                        'retCode': 0,
                        'result': {'list': [{'symbol': 'BTCUSDT', 'lastPrice': '50123.45'}]}
                    },
                    'coinbase': {
                        'timestamp': time.time(),
                        'data': [{'product_id': 'BTC-USD', 'price': '50123.45'}]
                    }
                },
                'balance_data': {
                    'bybit': {
                        'timestamp': time.time(),
                        'balances': {'BTC': Decimal('0.00123456'), 'USDT': Decimal('234.56')}
                    }
                },
                'market_data': {
                    'BTCUSDT': {
                        'timestamp': time.time(),
                        'technical_indicators': {
                            'sma': {
                                'timestamp': time.time(),
                                'values': [50100.0, 50150.0, 50123.45],
                                'data_source': 'live_api'
                            }
                        }
                    }
                },
                'trading_execution': {
                    'bybit': [{
                        'orderId': 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
                        'symbol': 'BTCUSDT',
                        'side': 'BUY',
                        'qty': '0.001',
                        'timestamp': time.time() * 1000
                    }]
                },
                'neural_data': {
                    'profit_predictor': {
                        'timestamp': time.time(),
                        'authenticity': {'is_real_data': True, 'data_source': 'live_trading'},
                        'outcomes': [{'transaction_id': 'real_tx_123', 'pnl': 12.34}]
                    }
                }
            }
            
            all_valid, validation_report = await self.master_validator.validate_system_wide_data_integrity(comprehensive_data)
            
            if not all_valid:
                return {'passed': False, 'error': f'System-wide validation failed: {validation_report}'}
            
            return {'passed': True, 'details': 'Complete system-wide validation working correctly'}
            
        except Exception as e:
            return {'passed': False, 'error': str(e)}
    
    async def generate_test_report(self):
        """Generate comprehensive test report"""
        success_rate = (self.passed_tests / max(self.test_count, 1)) * 100

        report = {
            'test_summary': {
                'total_tests': self.test_count,
                'passed_tests': self.passed_tests,
                'failed_tests': self.failed_tests,
                'success_rate_percent': round(success_rate, 2)
            },
            'test_results': self.test_results,
            'timestamp': datetime.now().isoformat()
        }

        # Add validation statistics if master validator is available
        if self.master_validator:
            try:
                report['validation_statistics'] = await self.master_validator._compile_validation_statistics()
                report['master_validator_report'] = self.master_validator.get_master_validation_report()
            except Exception as e:
                report['validation_statistics_error'] = str(e)
        else:
            report['validation_statistics'] = 'Master validator not initialized'
        
        # Save report to file
        report_file = Path(f"validation_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        # Print summary
        print("\n" + "="*80)
        print("🧪 REAL DATA VALIDATION SYSTEM TEST REPORT")
        print("="*80)
        print(f"📊 Total Tests: {self.test_count}")
        print(f"✅ Passed: {self.passed_tests}")
        print(f"❌ Failed: {self.failed_tests}")
        print(f"📈 Success Rate: {success_rate:.1f}%")
        print(f"📄 Report saved to: {report_file}")
        
        if self.failed_tests == 0:
            print("\n🎉 ALL TESTS PASSED - Real data validation system is working correctly!")
        else:
            print(f"\n⚠️ {self.failed_tests} tests failed - Review the report for details")
        
        print("="*80)

async def main():
    """Main test execution"""
    print("🚀 Starting Real Data Validation System Tests...")
    
    tester = RealDataValidationTester()
    await tester.run_comprehensive_tests()
    
    return tester.passed_tests == tester.test_count

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n🛑 Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test execution failed: {e}")
        sys.exit(1)
