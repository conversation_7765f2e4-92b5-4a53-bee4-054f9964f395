"""
API Data Validation Framework - Ensures all exchange API data is authentic and live
"""

import time
import logging
import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Union, Tuple
from decimal import Decimal
import json
import hashlib
import re

logger = logging.getLogger(__name__)

class APIDataValidator:
    """
    Comprehensive API data validator that ensures all exchange API responses
    contain only authentic, live data with proper timestamps and validation.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.validation_enabled = True
        self.fail_fast_mode = True
        self.max_response_age_seconds = 30  # API responses must be fresher than 30 seconds
        
        # Exchange-specific validation rules
        self.exchange_validators = {
            'bybit': self._validate_bybit_response,
            'coinbase': self._validate_coinbase_response,
            'binance': self._validate_binance_response
        }
        
        # Prohibited test endpoints
        self.test_endpoints = [
            'testnet.bybit.com',
            'sandbox.coinbase.com',
            'api.sandbox.',
            'test.api.',
            'demo.api.',
            'staging.api.',
            'localhost',
            '127.0.0.1'
        ]
        
        # Track API call validation history
        self.validation_history = []
        self.api_call_count = 0
        self.validation_failures = 0
        
        logger.info("🔒 [API-DATA-VALIDATOR] Initialized with strict real-data-only validation")
    
    async def validate_api_response(self, response: Dict[str, Any], exchange: str, endpoint: str, 
                                  request_timestamp: float) -> Tuple[bool, Dict[str, Any]]:
        """
        Validate API response for authenticity and freshness
        
        Returns:
            Tuple[bool, Dict[str, Any]]: (is_valid, validation_details)
        """
        validation_details = {
            'exchange': exchange,
            'endpoint': endpoint,
            'request_timestamp': request_timestamp,
            'validation_timestamp': time.time(),
            'checks_performed': [],
            'warnings': [],
            'errors': []
        }
        
        try:
            self.api_call_count += 1
            
            # 1. Validate response structure
            if not self._validate_response_structure(response, exchange, validation_details):
                return False, validation_details
            
            # 2. Validate timestamp freshness
            if not self._validate_timestamp_freshness(response, request_timestamp, validation_details):
                return False, validation_details
            
            # 3. Validate endpoint authenticity
            if not self._validate_endpoint_authenticity(endpoint, validation_details):
                return False, validation_details
            
            # 4. Exchange-specific validation
            if not await self._perform_exchange_specific_validation(response, exchange, validation_details):
                return False, validation_details
            
            # 5. Validate data authenticity
            if not self._validate_data_authenticity(response, validation_details):
                return False, validation_details
            
            # 6. Validate response consistency
            if not self._validate_response_consistency(response, exchange, validation_details):
                return False, validation_details
            
            validation_details['status'] = 'VALID'
            logger.debug(f"✅ [API-DATA-VALIDATOR] {exchange} API response validated successfully")
            return True, validation_details
            
        except Exception as e:
            validation_details['errors'].append(f"Validation exception: {str(e)}")
            validation_details['status'] = 'ERROR'
            self.validation_failures += 1
            
            if self.fail_fast_mode:
                logger.error(f"❌ [API-DATA-VALIDATOR] API validation failed for {exchange}: {e}")
                raise RuntimeError(f"CRITICAL: API data validation failed - {e}")
            else:
                logger.warning(f"⚠️ [API-DATA-VALIDATOR] API validation warning for {exchange}: {e}")
                return False, validation_details
    
    def _validate_response_structure(self, response: Dict[str, Any], exchange: str, 
                                   validation_details: Dict[str, Any]) -> bool:
        """Validate basic response structure"""
        validation_details['checks_performed'].append('response_structure')
        
        # Check if response is a dictionary
        if not isinstance(response, dict):
            validation_details['errors'].append("Response is not a dictionary")
            return False
        
        # Check for empty response
        if not response:
            validation_details['errors'].append("Response is empty")
            return False
        
        # Check for error indicators
        error_fields = ['error', 'errors', 'message', 'msg']
        for field in error_fields:
            if field in response and response[field]:
                validation_details['errors'].append(f"API returned error: {response[field]}")
                return False
        
        return True
    
    def _validate_timestamp_freshness(self, response: Dict[str, Any], request_timestamp: float,
                                    validation_details: Dict[str, Any]) -> bool:
        """Validate response timestamp is fresh"""
        validation_details['checks_performed'].append('timestamp_freshness')
        
        # Extract timestamp from response
        response_timestamp = self._extract_response_timestamp(response)
        current_time = time.time()
        
        if response_timestamp:
            # Check if response timestamp is reasonable
            age_seconds = current_time - response_timestamp
            if age_seconds > self.max_response_age_seconds:
                validation_details['errors'].append(
                    f"Response timestamp too old: {age_seconds:.1f}s > {self.max_response_age_seconds}s"
                )
                return False
            
            # Check if timestamp is not in the future
            if response_timestamp > current_time + 60:  # Allow 1 minute clock skew
                validation_details['errors'].append(
                    f"Response timestamp in future: {response_timestamp} > {current_time}"
                )
                return False
        else:
            # If no timestamp in response, check request-response latency
            response_latency = current_time - request_timestamp
            if response_latency > self.max_response_age_seconds:
                validation_details['warnings'].append(
                    f"No response timestamp, high latency: {response_latency:.1f}s"
                )
        
        validation_details['response_timestamp'] = response_timestamp
        validation_details['response_age_seconds'] = current_time - (response_timestamp or request_timestamp)
        
        return True
    
    def _validate_endpoint_authenticity(self, endpoint: str, validation_details: Dict[str, Any]) -> bool:
        """Validate endpoint is not a test/sandbox endpoint"""
        validation_details['checks_performed'].append('endpoint_authenticity')
        
        endpoint_lower = endpoint.lower()
        
        for test_endpoint in self.test_endpoints:
            if test_endpoint in endpoint_lower:
                validation_details['errors'].append(f"Test/sandbox endpoint detected: {test_endpoint}")
                return False
        
        # Check for localhost or IP addresses
        if any(pattern in endpoint_lower for pattern in ['localhost', '127.0.0.1', '192.168.', '10.0.']):
            validation_details['errors'].append(f"Local/private endpoint detected: {endpoint}")
            return False
        
        return True
    
    async def _perform_exchange_specific_validation(self, response: Dict[str, Any], exchange: str,
                                                  validation_details: Dict[str, Any]) -> bool:
        """Perform exchange-specific validation"""
        validation_details['checks_performed'].append('exchange_specific')
        
        exchange_lower = exchange.lower()
        if exchange_lower in self.exchange_validators:
            return await self.exchange_validators[exchange_lower](response, validation_details)
        else:
            validation_details['warnings'].append(f"No specific validator for exchange: {exchange}")
            return True
    
    async def _validate_bybit_response(self, response: Dict[str, Any], validation_details: Dict[str, Any]) -> bool:
        """Validate Bybit-specific response format"""
        # Check for Bybit response structure
        if 'retCode' in response:
            if response['retCode'] != 0:
                validation_details['errors'].append(f"Bybit API error: {response.get('retMsg', 'Unknown error')}")
                return False
        
        # Validate Bybit data structure
        if 'result' in response:
            result = response['result']
            if isinstance(result, dict):
                # Check for common Bybit fields
                if 'list' in result or 'category' in result or 'symbol' in result:
                    return True
        
        # Check for direct data (some endpoints return data directly)
        if any(field in response for field in ['symbol', 'price', 'volume', 'list']):
            return True
        
        validation_details['warnings'].append("Bybit response structure not recognized")
        return True
    
    async def _validate_coinbase_response(self, response: Dict[str, Any], validation_details: Dict[str, Any]) -> bool:
        """Validate Coinbase-specific response format"""
        # Check for Coinbase error structure
        if 'errors' in response and response['errors']:
            validation_details['errors'].append(f"Coinbase API errors: {response['errors']}")
            return False
        
        # Check for Coinbase data structure
        if 'data' in response:
            return True
        
        # Check for direct data (some endpoints return data directly)
        if any(field in response for field in ['id', 'product_id', 'price', 'size', 'time']):
            return True
        
        validation_details['warnings'].append("Coinbase response structure not recognized")
        return True
    
    async def _validate_binance_response(self, response: Dict[str, Any], validation_details: Dict[str, Any]) -> bool:
        """Validate Binance-specific response format"""
        # Check for Binance error structure
        if 'code' in response and response['code'] != 200:
            validation_details['errors'].append(f"Binance API error: {response.get('msg', 'Unknown error')}")
            return False
        
        # Check for common Binance fields
        if any(field in response for field in ['symbol', 'price', 'qty', 'time', 'serverTime']):
            return True
        
        validation_details['warnings'].append("Binance response structure not recognized")
        return True
    
    def _validate_data_authenticity(self, response: Dict[str, Any], validation_details: Dict[str, Any]) -> bool:
        """Validate data appears authentic and not hardcoded"""
        validation_details['checks_performed'].append('data_authenticity')
        
        # Convert response to string for pattern matching
        response_str = json.dumps(response, default=str).lower()
        
        # Check for mock/test indicators
        mock_indicators = ['mock', 'fake', 'test', 'dummy', 'sample', 'placeholder']
        for indicator in mock_indicators:
            if indicator in response_str:
                validation_details['errors'].append(f"Mock/test data indicator found: {indicator}")
                return False
        
        # Check for obviously fake prices
        fake_prices = [50000.0, 3000.0, 100.0, 1.0, 0.5]
        for price in fake_prices:
            if str(price) in response_str:
                validation_details['warnings'].append(f"Potentially hardcoded price found: {price}")
        
        return True
    
    def _validate_response_consistency(self, response: Dict[str, Any], exchange: str,
                                     validation_details: Dict[str, Any]) -> bool:
        """Validate response data consistency"""
        validation_details['checks_performed'].append('response_consistency')
        
        # Check for reasonable price ranges if price data is present
        price_fields = ['price', 'last', 'close', 'bid', 'ask']
        for field in price_fields:
            if field in response:
                try:
                    price = float(response[field])
                    if price <= 0:
                        validation_details['errors'].append(f"Invalid price value: {price}")
                        return False
                    if price > 1000000:  # Sanity check for extremely high prices
                        validation_details['warnings'].append(f"Unusually high price: {price}")
                except (ValueError, TypeError):
                    validation_details['warnings'].append(f"Non-numeric price value: {response[field]}")
        
        return True
    
    def _extract_response_timestamp(self, response: Dict[str, Any]) -> Optional[float]:
        """Extract timestamp from API response"""
        timestamp_fields = ['timestamp', 'time', 'serverTime', 'ts', 'created_at', 'updated_at']
        
        for field in timestamp_fields:
            if field in response:
                timestamp = response[field]
                try:
                    if isinstance(timestamp, (int, float)):
                        # Handle both seconds and milliseconds timestamps
                        if timestamp > 1e12:  # Milliseconds
                            return timestamp / 1000
                        else:  # Seconds
                            return float(timestamp)
                    elif isinstance(timestamp, str):
                        # Try parsing ISO format
                        dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                        return dt.timestamp()
                except (ValueError, TypeError):
                    continue
        
        return None
    
    def get_validation_statistics(self) -> Dict[str, Any]:
        """Get validation statistics"""
        success_rate = ((self.api_call_count - self.validation_failures) / max(self.api_call_count, 1)) * 100
        
        return {
            'total_api_calls': self.api_call_count,
            'validation_failures': self.validation_failures,
            'success_rate_percent': round(success_rate, 2),
            'validator_status': 'ACTIVE' if self.validation_enabled else 'DISABLED',
            'fail_fast_mode': self.fail_fast_mode,
            'max_response_age_seconds': self.max_response_age_seconds,
            'supported_exchanges': list(self.exchange_validators.keys())
        }
