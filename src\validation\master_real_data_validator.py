"""
Master Real Data Validator - Comprehensive integration of all validation systems
"""

import time
import logging
import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Union, Tuple
from decimal import Decimal
import json

# Import all validation components
from .real_data_validator import RealDataValidator
from .api_data_validator import APIDataValidator
from .trading_execution_validator import TradingExecutionValidator
from .balance_verification_system import BalanceVerificationSystem
from .market_data_integrity_validator import MarketDataIntegrityValidator
from .neural_learning_data_validator import NeuralLearningDataValidator
from .fail_fast_error_handler import FailFastErrorHandler
from .runtime_validation_checkpoints import RuntimeValidationCheckpoints
from .data_recovery_system import DataRecoverySystem
from .real_data_stream_manager import RealDataStreamManager
from .fake_data_purge_system import FakeDataPurgeSystem
from .automatic_data_source_recovery import AutomaticDataSourceRecovery
from .continuous_trading_resilience import ContinuousTradingResilience

logger = logging.getLogger(__name__)

class MasterRealDataValidator:
    """
    Master validator that integrates all validation systems to ensure 100% real-data-only operation
    throughout the entire autogpt-trader system with zero tolerance for fake, mock, or simulated data.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.validation_enabled = True
        self.fail_fast_mode = True

        # Initialize all validation components with error handling
        try:
            self.real_data_validator = RealDataValidator(self.config.get('real_data', {}))
            self.api_validator = APIDataValidator(self.config.get('api_data', {}))
            self.execution_validator = TradingExecutionValidator(self.config.get('execution', {}))
            self.balance_verifier = BalanceVerificationSystem(self.config.get('balance', {}))
            self.market_data_validator = MarketDataIntegrityValidator(self.config.get('market_data', {}))
            self.neural_validator = NeuralLearningDataValidator(self.config.get('neural', {}))
            self.error_handler = FailFastErrorHandler(self.config.get('error_handling', {}))
            self.runtime_checkpoints = RuntimeValidationCheckpoints(self.config.get('runtime', {}))

            # Initialize recovery and resilience systems
            self.data_recovery_system = DataRecoverySystem(self.config.get('data_recovery', {}))
            self.stream_manager = RealDataStreamManager(self.config.get('stream_manager', {}))
            self.purge_system = FakeDataPurgeSystem(self.config.get('purge_system', {}))
            self.source_recovery = AutomaticDataSourceRecovery(self.config.get('source_recovery', {}))
            self.trading_resilience = ContinuousTradingResilience(self.config.get('trading_resilience', {}))

            # Integrate recovery systems with error handler
            self.error_handler.integrate_recovery_systems(
                data_recovery_system=self.data_recovery_system,
                stream_manager=self.stream_manager,
                purge_system=self.purge_system
            )

        except Exception as e:
            logger.error(f"❌ [MASTER-VALIDATOR] Failed to initialize validation components: {e}")
            raise RuntimeError(f"Master validator initialization failed: {e}")
        
        # Master validation statistics
        self.total_validations = 0
        self.total_failures = 0
        self.validation_history = []
        self.system_status = "ACTIVE"
        
        # Validation pipeline configuration
        self.validation_pipeline = {
            'api_responses': self._validate_api_response_pipeline,
            'balance_data': self._validate_balance_pipeline,
            'market_data': self._validate_market_data_pipeline,
            'trading_execution': self._validate_execution_pipeline,
            'neural_data': self._validate_neural_pipeline,
            'comprehensive': self._validate_comprehensive_pipeline
        }
        
        logger.info("🔒 [MASTER-VALIDATOR] Initialized comprehensive real-data-only validation system with intelligent recovery")

    async def detect_fake_data_and_recover(self, data_sources: Dict[str, Any]) -> Tuple[bool, Dict[str, Any]]:
        """
        Detect fake data, purge it, and automatically recover real data streams.
        This is the new primary method that replaces system halt with intelligent recovery.
        """
        try:
            logger.info("🔍 [MASTER-VALIDATOR] Scanning for fake data and initiating recovery if needed...")

            recovery_report = {
                'timestamp': time.time(),
                'total_sources_scanned': len(data_sources),
                'fake_data_detected': [],
                'recovery_actions': [],
                'purge_results': [],
                'stream_restorations': [],
                'overall_status': 'SCANNING'
            }

            all_sources_valid = True
            recovered_data_sources = {}

            # Scan each data source for fake data
            for source_name, source_data in data_sources.items():
                try:
                    # First, scan for fake data patterns
                    cleaned_data, purge_report = await self.purge_system.scan_and_purge_fake_data(
                        {source_name: source_data}, source_name
                    )

                    if purge_report:
                        recovery_report['fake_data_detected'].append({
                            'source': source_name,
                            'purge_report': purge_report
                        })
                        recovery_report['purge_results'].append(f"Purged fake data from {source_name}")

                        # Attempt to recover real data
                        logger.info(f"🔄 [MASTER-VALIDATOR] Recovering real data for {source_name}...")

                        recovery_success, recovered_data = await self.data_recovery_system.detect_and_recover_fake_data(
                            source_name, source_data, purge_report
                        )

                        if recovery_success:
                            recovered_data_sources[source_name] = recovered_data
                            recovery_report['recovery_actions'].append(f"Successfully recovered {source_name}")
                            recovery_report['stream_restorations'].append({
                                'source': source_name,
                                'status': 'SUCCESS',
                                'recovered_data_size': len(str(recovered_data))
                            })
                            logger.info(f"✅ [MASTER-VALIDATOR] Successfully recovered real data for {source_name}")
                        else:
                            all_sources_valid = False
                            recovery_report['recovery_actions'].append(f"Failed to recover {source_name}")
                            recovery_report['stream_restorations'].append({
                                'source': source_name,
                                'status': 'FAILED',
                                'error': 'Recovery failed'
                            })
                            logger.error(f"❌ [MASTER-VALIDATOR] Failed to recover real data for {source_name}")
                    else:
                        # No fake data detected, use original data
                        recovered_data_sources[source_name] = source_data

                except Exception as e:
                    logger.error(f"❌ [MASTER-VALIDATOR] Error processing {source_name}: {e}")
                    all_sources_valid = False
                    recovery_report['recovery_actions'].append(f"Error processing {source_name}: {e}")

            # Update overall status
            if all_sources_valid:
                recovery_report['overall_status'] = 'ALL_SOURCES_VALID'
                logger.info("✅ [MASTER-VALIDATOR] All data sources validated and recovered successfully")
            else:
                recovery_report['overall_status'] = 'PARTIAL_RECOVERY'
                logger.warning("⚠️ [MASTER-VALIDATOR] Some data sources could not be fully recovered")

            # Update recovered data sources
            recovery_report['recovered_data_sources'] = list(recovered_data_sources.keys())
            recovery_report['total_recovered_sources'] = len(recovered_data_sources)

            return all_sources_valid, recovery_report

        except Exception as e:
            logger.error(f"❌ [MASTER-VALIDATOR] Fake data detection and recovery failed: {e}")
            return False, {
                'error': str(e),
                'overall_status': 'RECOVERY_FAILED',
                'timestamp': time.time()
            }

    async def validate_system_wide_data_integrity(self, data_sources: Dict[str, Any]) -> Tuple[bool, Dict[str, Any]]:
        """
        Perform system-wide validation across all data sources
        
        Returns:
            Tuple[bool, Dict[str, Any]]: (all_valid, comprehensive_report)
        """
        validation_report = {
            'validation_timestamp': time.time(),
            'total_data_sources': len(data_sources),
            'validation_results': {},
            'overall_status': 'PENDING',
            'critical_failures': [],
            'warnings': [],
            'statistics': {}
        }
        
        try:
            self.total_validations += 1
            
            # Check if system is halted
            if self.error_handler.is_system_halted():
                validation_report['overall_status'] = 'SYSTEM_HALTED'
                validation_report['halt_reason'] = self.error_handler.get_halt_reason()
                return False, validation_report
            
            # Validate each data source category
            all_valid = True
            
            for source_type, source_data in data_sources.items():
                if source_type in self.validation_pipeline:
                    try:
                        is_valid, details = await self.validation_pipeline[source_type](source_data)
                        validation_report['validation_results'][source_type] = {
                            'valid': is_valid,
                            'details': details
                        }
                        
                        if not is_valid:
                            all_valid = False
                            validation_report['critical_failures'].append({
                                'source_type': source_type,
                                'details': details
                            })
                    
                    except Exception as e:
                        all_valid = False
                        error_context = {
                            'source_type': source_type,
                            'validation_active': True,
                            'system_wide_validation': True
                        }
                        
                        # Handle validation error through error handler
                        should_continue = await self.error_handler.handle_error(e, error_context, 'master_validator')
                        if not should_continue:
                            validation_report['overall_status'] = 'SYSTEM_HALTED'
                            return False, validation_report
                        
                        validation_report['validation_results'][source_type] = {
                            'valid': False,
                            'error': str(e)
                        }
            
            # Perform cross-validation checks
            cross_validation_result = await self._perform_cross_validation(data_sources)
            validation_report['cross_validation'] = cross_validation_result
            
            if not cross_validation_result['valid']:
                all_valid = False
            
            # Update statistics
            validation_report['statistics'] = await self._compile_validation_statistics()
            
            # Set overall status
            if all_valid:
                validation_report['overall_status'] = 'ALL_VALID'
                logger.info("✅ [MASTER-VALIDATOR] System-wide validation PASSED - all data sources authentic")
            else:
                validation_report['overall_status'] = 'VALIDATION_FAILED'
                self.total_failures += 1
                logger.error(f"❌ [MASTER-VALIDATOR] System-wide validation FAILED - {len(validation_report['critical_failures'])} critical failures")
            
            # Store validation history
            self.validation_history.append({
                'timestamp': time.time(),
                'result': all_valid,
                'data_sources_count': len(data_sources),
                'failures_count': len(validation_report['critical_failures'])
            })
            
            return all_valid, validation_report
            
        except Exception as e:
            self.total_failures += 1
            validation_report['overall_status'] = 'VALIDATION_ERROR'
            validation_report['error'] = str(e)
            
            logger.error(f"❌ [MASTER-VALIDATOR] System-wide validation error: {e}")
            
            # Handle critical validation error
            error_context = {'system_wide_validation': True, 'critical_validation_failure': True}
            await self.error_handler.handle_error(e, error_context, 'master_validator')
            
            return False, validation_report
    
    async def _validate_api_response_pipeline(self, api_data: Dict[str, Any]) -> Tuple[bool, Dict[str, Any]]:
        """Validate API response data through complete pipeline"""
        pipeline_result = {
            'pipeline': 'api_response',
            'stages': {},
            'overall_valid': True
        }
        
        for exchange, response_data in api_data.items():
            # Stage 1: API Data Validator
            api_valid, api_details = await self.api_validator.validate_api_response(
                response_data, exchange, 'live_endpoint', time.time()
            )
            pipeline_result['stages'][f'{exchange}_api'] = {'valid': api_valid, 'details': api_details}
            
            # Stage 2: Real Data Validator
            real_data_valid = self.real_data_validator.validate_api_response_authenticity(response_data, exchange)
            pipeline_result['stages'][f'{exchange}_real_data'] = {'valid': real_data_valid}
            
            # Stage 3: Runtime Checkpoints
            self.runtime_checkpoints.assert_data_authenticity(response_data, f'{exchange}_api_response')
            
            if not (api_valid and real_data_valid):
                pipeline_result['overall_valid'] = False
        
        return pipeline_result['overall_valid'], pipeline_result
    
    async def _validate_balance_pipeline(self, balance_data: Dict[str, Any]) -> Tuple[bool, Dict[str, Any]]:
        """Validate balance data through complete pipeline"""
        pipeline_result = {
            'pipeline': 'balance_data',
            'stages': {},
            'overall_valid': True
        }
        
        for exchange, balance_info in balance_data.items():
            # Stage 1: Balance Verification System
            balance_valid, balance_details = await self.balance_verifier.verify_balance_authenticity(
                balance_info, exchange, time.time()
            )
            pipeline_result['stages'][f'{exchange}_balance'] = {'valid': balance_valid, 'details': balance_details}
            
            # Stage 2: Real Data Validator
            real_balance_valid = self.real_data_validator.validate_balance_data(balance_info, exchange)
            pipeline_result['stages'][f'{exchange}_real_balance'] = {'valid': real_balance_valid}
            
            # Stage 3: Placeholder Detection
            for currency, amount in balance_info.get('balances', {}).items():
                placeholder_valid = self.error_handler.validate_no_placeholder_substitution(
                    amount, 'balances', f'{exchange}_{currency}'
                )
                if not placeholder_valid:
                    pipeline_result['overall_valid'] = False
            
            if not (balance_valid and real_balance_valid):
                pipeline_result['overall_valid'] = False
        
        return pipeline_result['overall_valid'], pipeline_result
    
    async def _validate_market_data_pipeline(self, market_data: Dict[str, Any]) -> Tuple[bool, Dict[str, Any]]:
        """Validate market data through complete pipeline"""
        pipeline_result = {
            'pipeline': 'market_data',
            'stages': {},
            'overall_valid': True
        }
        
        for symbol, data in market_data.items():
            # Stage 1: Market Data Integrity Validator
            if 'technical_indicators' in data:
                for indicator_type, indicator_data in data['technical_indicators'].items():
                    indicator_valid, indicator_details = await self.market_data_validator.validate_technical_indicator_data(
                        indicator_data, indicator_type, symbol
                    )
                    pipeline_result['stages'][f'{symbol}_{indicator_type}'] = {
                        'valid': indicator_valid, 'details': indicator_details
                    }
                    
                    if not indicator_valid:
                        pipeline_result['overall_valid'] = False
            
            # Stage 2: Real Data Validator
            market_valid = self.real_data_validator.validate_market_data(data, symbol)
            pipeline_result['stages'][f'{symbol}_market'] = {'valid': market_valid}
            
            if not market_valid:
                pipeline_result['overall_valid'] = False
        
        return pipeline_result['overall_valid'], pipeline_result
    
    async def _validate_execution_pipeline(self, execution_data: Dict[str, Any]) -> Tuple[bool, Dict[str, Any]]:
        """Validate trading execution data through complete pipeline"""
        pipeline_result = {
            'pipeline': 'trading_execution',
            'stages': {},
            'overall_valid': True
        }
        
        for exchange, orders in execution_data.items():
            if isinstance(orders, list):
                for i, order in enumerate(orders):
                    # Stage 1: Trading Execution Validator
                    execution_valid, execution_details = await self.execution_validator.validate_order_execution(
                        order, exchange, time.time()
                    )
                    pipeline_result['stages'][f'{exchange}_order_{i}'] = {
                        'valid': execution_valid, 'details': execution_details
                    }
                    
                    # Stage 2: Real Data Validator
                    order_valid = self.real_data_validator.validate_order_execution_data(order, exchange)
                    pipeline_result['stages'][f'{exchange}_order_{i}_real'] = {'valid': order_valid}
                    
                    if not (execution_valid and order_valid):
                        pipeline_result['overall_valid'] = False
        
        return pipeline_result['overall_valid'], pipeline_result
    
    async def _validate_neural_pipeline(self, neural_data: Dict[str, Any]) -> Tuple[bool, Dict[str, Any]]:
        """Validate neural learning data through complete pipeline"""
        pipeline_result = {
            'pipeline': 'neural_learning',
            'stages': {},
            'overall_valid': True
        }
        
        for model_name, training_data in neural_data.items():
            # Stage 1: Neural Learning Data Validator
            neural_valid, neural_details = await self.neural_validator.validate_training_data(
                training_data, model_name, 'trading_outcomes'
            )
            pipeline_result['stages'][f'{model_name}_neural'] = {
                'valid': neural_valid, 'details': neural_details
            }
            
            # Stage 2: Real Data Validator
            input_valid = self.real_data_validator.validate_neural_input_data(training_data)
            pipeline_result['stages'][f'{model_name}_input'] = {'valid': input_valid}
            
            if not (neural_valid and input_valid):
                pipeline_result['overall_valid'] = False
        
        return pipeline_result['overall_valid'], pipeline_result
    
    async def _validate_comprehensive_pipeline(self, all_data: Dict[str, Any]) -> Tuple[bool, Dict[str, Any]]:
        """Perform comprehensive cross-system validation"""
        comprehensive_result = await self.real_data_validator.perform_comprehensive_validation(all_data)
        return comprehensive_result['overall_status'], comprehensive_result
    
    async def _perform_cross_validation(self, data_sources: Dict[str, Any]) -> Dict[str, Any]:
        """Perform cross-validation between different data sources"""
        cross_validation = {
            'valid': True,
            'checks': [],
            'inconsistencies': []
        }
        
        # Check timestamp consistency across sources
        timestamps = {}
        for source_type, source_data in data_sources.items():
            if isinstance(source_data, dict):
                for key, data in source_data.items():
                    if isinstance(data, dict):
                        timestamp = self._extract_timestamp(data)
                        if timestamp:
                            timestamps[f'{source_type}_{key}'] = timestamp
        
        # Check for timestamp inconsistencies
        if timestamps:
            timestamp_values = list(timestamps.values())
            max_timestamp = max(timestamp_values)
            min_timestamp = min(timestamp_values)
            
            if max_timestamp - min_timestamp > 60:  # More than 1 minute difference
                cross_validation['inconsistencies'].append({
                    'type': 'timestamp_inconsistency',
                    'max_diff_seconds': max_timestamp - min_timestamp,
                    'sources': timestamps
                })
                cross_validation['valid'] = False
        
        cross_validation['checks'].append('timestamp_consistency')
        
        return cross_validation
    
    def _extract_timestamp(self, data: Dict[str, Any]) -> Optional[float]:
        """Extract timestamp from data structure"""
        timestamp_fields = ['timestamp', 'time', 'created_at', 'updated_at']
        
        for field in timestamp_fields:
            if field in data:
                timestamp = data[field]
                try:
                    if isinstance(timestamp, (int, float)):
                        if timestamp > 1e12:  # Milliseconds
                            return timestamp / 1000
                        else:  # Seconds
                            return float(timestamp)
                    elif isinstance(timestamp, str):
                        dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                        return dt.timestamp()
                except (ValueError, TypeError):
                    continue
        
        return None
    
    async def _compile_validation_statistics(self) -> Dict[str, Any]:
        """Compile comprehensive validation statistics"""
        try:
            return {
                'real_data_validator': self.real_data_validator.get_validation_report() if hasattr(self.real_data_validator, 'get_validation_report') else {},
                'api_validator': self.api_validator.get_validation_statistics() if hasattr(self.api_validator, 'get_validation_statistics') else {},
                'execution_validator': self.execution_validator.get_execution_statistics() if hasattr(self.execution_validator, 'get_execution_statistics') else {},
                'balance_verifier': self.balance_verifier.get_verification_statistics() if hasattr(self.balance_verifier, 'get_verification_statistics') else {},
                'market_data_validator': self.market_data_validator.get_integrity_statistics() if hasattr(self.market_data_validator, 'get_integrity_statistics') else {},
                'neural_validator': self.neural_validator.get_learning_validation_statistics() if hasattr(self.neural_validator, 'get_learning_validation_statistics') else {},
                'error_handler': self.error_handler.get_error_statistics() if hasattr(self.error_handler, 'get_error_statistics') else {},
                'runtime_checkpoints': self.runtime_checkpoints.get_checkpoint_statistics() if hasattr(self.runtime_checkpoints, 'get_checkpoint_statistics') else {},
                'master_validator': {
                    'total_validations': self.total_validations,
                    'total_failures': self.total_failures,
                    'success_rate_percent': ((self.total_validations - self.total_failures) /
                                           max(self.total_validations, 1)) * 100,
                    'system_status': self.system_status
                }
            }
        except Exception as e:
            logger.error(f"❌ [MASTER-VALIDATOR] Error compiling statistics: {e}")
            return {
                'error': str(e),
                'master_validator': {
                    'total_validations': self.total_validations,
                    'total_failures': self.total_failures,
                    'system_status': self.system_status
                }
            }
    
    async def emergency_validation_halt(self, reason: str):
        """Emergency halt of all validation systems"""
        self.system_status = "EMERGENCY_HALT"
        
        # Halt all validation components
        self.validation_enabled = False
        self.real_data_validator.validation_enabled = False
        self.api_validator.validation_enabled = False
        self.execution_validator.validation_enabled = False
        self.balance_verifier.validation_enabled = False
        self.market_data_validator.validation_enabled = False
        self.neural_validator.validation_enabled = False
        self.runtime_checkpoints.validation_enabled = False
        
        # Trigger error handler halt
        await self.error_handler._halt_system(f"Emergency validation halt: {reason}", {
            'emergency_halt': True,
            'reason': reason,
            'timestamp': time.time()
        })
        
        logger.critical(f"🛑 [EMERGENCY-HALT] Master validator emergency halt: {reason}")
    
    def get_master_validation_report(self) -> Dict[str, Any]:
        """Get comprehensive master validation report"""
        recent_validations = [
            v for v in self.validation_history 
            if time.time() - v['timestamp'] < 3600  # Last hour
        ]
        
        return {
            'master_validator_status': self.system_status,
            'validation_enabled': self.validation_enabled,
            'fail_fast_mode': self.fail_fast_mode,
            'total_validations': self.total_validations,
            'total_failures': self.total_failures,
            'recent_validations': len(recent_validations),
            'system_halted': self.error_handler.is_system_halted(),
            'halt_reason': self.error_handler.get_halt_reason(),
            'validation_components': {
                'real_data_validator': 'ACTIVE' if self.real_data_validator.validation_enabled else 'DISABLED',
                'api_validator': 'ACTIVE' if self.api_validator.validation_enabled else 'DISABLED',
                'execution_validator': 'ACTIVE' if self.execution_validator.validation_enabled else 'DISABLED',
                'balance_verifier': 'ACTIVE' if self.balance_verifier.validation_enabled else 'DISABLED',
                'market_data_validator': 'ACTIVE' if self.market_data_validator.validation_enabled else 'DISABLED',
                'neural_validator': 'ACTIVE' if self.neural_validator.validation_enabled else 'DISABLED',
                'error_handler': 'ACTIVE' if self.error_handler.fail_fast_enabled else 'DISABLED',
                'runtime_checkpoints': 'ACTIVE' if self.runtime_checkpoints.validation_enabled else 'DISABLED'
            }
        }
