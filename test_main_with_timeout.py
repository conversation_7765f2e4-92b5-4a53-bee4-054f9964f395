#!/usr/bin/env python3
"""
Test main.py with timeout to identify where it hangs
"""
import asyncio
import sys
import signal
import time
from concurrent.futures import Thread<PERSON>oolExecutor, TimeoutError as FutureTimeoutError

def timeout_handler(signum, frame):
    print("❌ TIMEOUT: Main function took too long to execute")
    sys.exit(1)

async def run_main_with_timeout():
    """Run main function with timeout (Windows compatible)"""
    print("🔍 TESTING MAIN.PY WITH 60-SECOND TIMEOUT")
    print("=" * 50)

    try:
        # Import main module
        print("📋 [STEP-1] Importing main module...")
        import main
        print("✅ [STEP-1] Main module imported successfully")

        print("📋 [STEP-2] Running main() function with asyncio timeout...")
        start_time = time.time()

        try:
            # Run the main function with asyncio timeout (Windows compatible)
            result = await asyncio.wait_for(main.main(), timeout=60.0)

            end_time = time.time()
            execution_time = end_time - start_time

            print(f"✅ [SUCCESS] Main function completed in {execution_time:.2f} seconds")
            print(f"📊 [RESULT] Return value: {result}")
            return True

        except asyncio.TimeoutError:
            print("❌ [TIMEOUT] Main function timed out after 60 seconds")
            return False
        except Exception as e:
            print(f"❌ [ERROR] Main function failed: {e}")
            import traceback
            traceback.print_exc()
            return False

    except Exception as e:
        print(f"❌ [IMPORT-ERROR] Failed to import main: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    try:
        success = asyncio.run(run_main_with_timeout())
        if success:
            print("🎉 MAIN.PY TEST COMPLETED SUCCESSFULLY")
        else:
            print("❌ MAIN.PY TEST FAILED")
        return success
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
        return False
    except Exception as e:
        print(f"❌ Test crashed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
