#!/usr/bin/env python3
"""
Comprehensive test for all system fixes
"""
import os
import sys
import asyncio
import logging
from dotenv import load_dotenv

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger("SystemFixesTest")

async def test_tensorflow_fixes():
    """Test TensorFlow KerasCompatibility fixes"""
    logger.info("🧪 [TEST-1] Testing TensorFlow KerasCompatibility fixes...")
    
    try:
        from src.neural.tensorflow_alternative import tf_alternative
        
        # Test keras attribute exists
        assert hasattr(tf_alternative, 'keras'), "keras attribute missing"
        
        # Test optimizers attribute exists
        assert hasattr(tf_alternative.keras, 'optimizers'), "optimizers attribute missing"
        
        # Test Adam optimizer
        adam_opt = tf_alternative.keras.optimizers.Adam(learning_rate=0.001)
        assert adam_opt['type'] == 'Adam', "Adam optimizer not working"
        
        logger.info("✅ [TEST-1] TensorFlow KerasCompatibility fixes WORKING")
        return True
        
    except Exception as e:
        logger.error(f"❌ [TEST-1] TensorFlow fixes test failed: {e}")
        return False

async def test_cryptography_fixes():
    """Test cryptography decryption fixes"""
    logger.info("🧪 [TEST-2] Testing cryptography decryption fixes...")
    
    try:
        from src.utils.cryptography.secure_credentials import decrypt_value
        
        # Test with invalid encrypted value that looks encrypted (should return None now)
        result = decrypt_value("gAAAAABinvalid_encrypted_value_that_will_fail_decryption")
        assert result is None, "Should return None for invalid decryption"
        
        logger.info("✅ [TEST-2] Cryptography decryption fixes WORKING")
        return True
        
    except Exception as e:
        logger.error(f"❌ [TEST-2] Cryptography fixes test failed: {e}")
        return False

async def test_system_self_learning_fixes():
    """Test SystemSelfLearningFramework fixes"""
    logger.info("🧪 [TEST-3] Testing SystemSelfLearningFramework fixes...")
    
    try:
        from src.learning.system_self_learning_framework import SystemSelfLearningFramework
        
        # Test initialization with config parameter (not learning_config)
        framework = SystemSelfLearningFramework(config={
            'continuous_learning': True,
            'meta_learning_enabled': True
        })
        
        assert framework.config['continuous_learning'] == True, "Config not properly set"
        
        logger.info("✅ [TEST-3] SystemSelfLearningFramework fixes WORKING")
        return True
        
    except Exception as e:
        logger.error(f"❌ [TEST-3] SystemSelfLearningFramework fixes test failed: {e}")
        return False

async def test_bybit_ip_bypass():
    """Test Bybit IP bypass functionality"""
    logger.info("🧪 [TEST-4] Testing Bybit IP bypass functionality...")
    
    try:
        load_dotenv()
        
        # Check environment variables
        ip_bypass = os.getenv('BYBIT_IP_BYPASS', 'false').lower() in ['true', '1', 'yes', 'on']
        force_real_trading = os.getenv('FORCE_REAL_TRADING', 'false').lower() in ['true', '1', 'yes', 'on']
        
        assert ip_bypass == True, "BYBIT_IP_BYPASS not enabled"
        assert force_real_trading == True, "FORCE_REAL_TRADING not enabled"
        
        # Test client creation with bypass
        api_key = os.getenv('BYBIT_API_KEY')
        api_secret = os.getenv('BYBIT_API_SECRET')
        
        if api_key and api_secret:
            from src.exchanges.bybit_client_fixed import BybitClientFixed
            
            client = BybitClientFixed(api_key=api_key, api_secret=api_secret, testnet=False)
            assert client.ip_bypass_enabled == True, "IP bypass not enabled in client"
            assert client.force_real_trading == True, "Force real trading not enabled in client"
            
            logger.info("✅ [TEST-4] Bybit IP bypass functionality WORKING")
            return True
        else:
            logger.warning("⚠️ [TEST-4] Bybit credentials not available - skipping client test")
            return True
        
    except Exception as e:
        logger.error(f"❌ [TEST-4] Bybit IP bypass test failed: {e}")
        return False

async def test_validation_fixes():
    """Test validation result fixes"""
    logger.info("🧪 [TEST-5] Testing validation result fixes...")
    
    try:
        from src.validation.real_money_enforcer import RealMoneyTradingEnforcer
        
        # Test that validate() returns boolean
        enforcer = RealMoneyTradingEnforcer()
        result = await enforcer.validate()
        
        assert isinstance(result, bool), f"validate() should return bool, got {type(result)}"
        
        logger.info("✅ [TEST-5] Validation result fixes WORKING")
        return True
        
    except Exception as e:
        logger.error(f"❌ [TEST-5] Validation fixes test failed: {e}")
        return False

async def test_enhanced_profit_predictor():
    """Test EnhancedProfitPredictor with TensorFlow alternative"""
    logger.info("🧪 [TEST-6] Testing EnhancedProfitPredictor with TensorFlow alternative...")
    
    try:
        from src.neural.enhanced_profit_predictor import EnhancedProfitPredictor
        
        # Test initialization (should not crash with KerasCompatibility error)
        predictor = EnhancedProfitPredictor()
        assert predictor is not None, "EnhancedProfitPredictor failed to initialize"
        
        logger.info("✅ [TEST-6] EnhancedProfitPredictor WORKING")
        return True
        
    except Exception as e:
        logger.error(f"❌ [TEST-6] EnhancedProfitPredictor test failed: {e}")
        return False

async def run_comprehensive_tests():
    """Run all comprehensive tests"""
    logger.info("🔍 COMPREHENSIVE SYSTEM FIXES TEST")
    logger.info("=" * 50)
    
    tests = [
        ("TensorFlow KerasCompatibility", test_tensorflow_fixes),
        ("Cryptography Decryption", test_cryptography_fixes),
        ("SystemSelfLearningFramework", test_system_self_learning_fixes),
        ("Bybit IP Bypass", test_bybit_ip_bypass),
        ("Validation Results", test_validation_fixes),
        ("EnhancedProfitPredictor", test_enhanced_profit_predictor),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results[test_name] = result
        except Exception as e:
            logger.error(f"❌ {test_name} test crashed: {e}")
            results[test_name] = False
    
    # Summary
    logger.info("\n📊 TEST RESULTS SUMMARY")
    logger.info("=" * 30)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{status} - {test_name}")
        if result:
            passed += 1
    
    logger.info(f"\n📈 OVERALL: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        logger.info("🎉 ALL FIXES WORKING CORRECTLY!")
        return True
    else:
        logger.warning(f"⚠️ {total-passed} tests failed - manual intervention may be required")
        return False

def main():
    """Main test function"""
    try:
        success = asyncio.run(run_comprehensive_tests())
        sys.exit(0 if success else 1)
    except Exception as e:
        logger.error(f"❌ Test suite crashed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
