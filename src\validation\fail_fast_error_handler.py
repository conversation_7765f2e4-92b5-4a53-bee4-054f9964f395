"""
Fail-Fast Error Handler - Halts trading when real data unavailable, prevents placeholder substitution
"""

import time
import logging
import asyncio
import traceback
import sys
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Union, Callable
from decimal import Decimal
import json
import os
from pathlib import Path

logger = logging.getLogger(__name__)

class FailFastErrorHandler:
    """
    Error handler that implements fail-fast mechanisms to halt trading when real data is unavailable.
    Prevents placeholder value substitution and ensures system integrity.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.fail_fast_enabled = True
        self.recovery_mode_enabled = True  # Enable recovery instead of halt
        self.critical_error_threshold = 3  # Max critical errors before system halt
        self.error_recovery_timeout = 300  # 5 minutes recovery timeout
        
        # Error tracking
        self.critical_errors = []
        self.error_count = 0
        self.last_critical_error = None
        self.system_halted = False
        self.halt_reason = None
        
        # Critical error patterns that should halt trading
        self.critical_patterns = {
            'data_unavailable': [
                'api.*unavailable', 'connection.*failed', 'timeout.*exceeded',
                'no.*data.*available', 'data.*fetch.*failed', 'empty.*response'
            ],
            'authentication_failed': [
                'authentication.*failed', 'invalid.*credentials', 'unauthorized',
                'api.*key.*invalid', 'permission.*denied', 'access.*denied'
            ],
            'balance_errors': [
                'insufficient.*balance', 'balance.*fetch.*failed', 'account.*suspended',
                'balance.*unavailable', 'wallet.*error'
            ],
            'trading_errors': [
                'order.*failed', 'execution.*failed', 'trading.*suspended',
                'market.*closed', 'symbol.*unavailable'
            ],
            'validation_failures': [
                'validation.*failed', 'data.*integrity.*error', 'fake.*data.*detected',
                'simulation.*mode.*detected', 'test.*data.*found'
            ]
        }
        
        # Prohibited placeholder values
        self.prohibited_placeholders = {
            'prices': [0.0, 1.0, 100.0, 1000.0, 50000.0, 3000.0],
            'balances': [0.0, 10.0, 100.0, 1000.0, 10000.0],
            'volumes': [0.0, 1.0, 1000.0, 1000000.0],
            'strings': ['N/A', 'null', 'undefined', 'placeholder', 'default', 'mock', 'test']
        }
        
        # Error handlers by category (now with recovery)
        self.error_handlers = {
            'data_unavailable': self._handle_data_unavailable_with_recovery,
            'authentication_failed': self._handle_authentication_failed,
            'balance_errors': self._handle_balance_errors_with_recovery,
            'trading_errors': self._handle_trading_errors_with_recovery,
            'validation_failures': self._handle_validation_failures_with_recovery
        }

        # Recovery system integration
        self.data_recovery_system = None
        self.stream_manager = None
        self.purge_system = None
        
        # Setup critical error logging
        self._setup_critical_error_logging()
        
        logger.info("🔒 [FAIL-FAST-HANDLER] Initialized with intelligent recovery and data restoration capabilities")
    
    def _setup_critical_error_logging(self):
        """Setup dedicated critical error logging"""
        try:
            # Create critical errors directory
            error_dir = Path("logs/critical_errors")
            error_dir.mkdir(parents=True, exist_ok=True)
            
            # Setup critical error logger
            self.critical_logger = logging.getLogger("critical_errors")
            self.critical_logger.setLevel(logging.ERROR)
            
            # Add file handler for critical errors
            error_file = error_dir / f"critical_errors_{datetime.now().strftime('%Y%m%d')}.log"
            file_handler = logging.FileHandler(error_file, encoding='utf-8')
            file_handler.setLevel(logging.ERROR)
            
            formatter = logging.Formatter(
                '%(asctime)s - [CRITICAL] - %(name)s - %(message)s'
            )
            file_handler.setFormatter(formatter)
            
            self.critical_logger.addHandler(file_handler)
            
        except Exception as e:
            logger.warning(f"⚠️ [FAIL-FAST-HANDLER] Could not setup critical error logging: {e}")
            self.critical_logger = logger

    def integrate_recovery_systems(self, data_recovery_system=None, stream_manager=None, purge_system=None):
        """Integrate recovery systems for intelligent error handling"""
        self.data_recovery_system = data_recovery_system
        self.stream_manager = stream_manager
        self.purge_system = purge_system

        logger.info("🔧 [FAIL-FAST-HANDLER] Integrated recovery systems - now using recovery instead of halt")

    async def handle_error(self, error: Exception, context: Dict[str, Any],
                          component: str = "unknown") -> bool:
        """
        Handle error with fail-fast logic
        
        Returns:
            bool: True if system should continue, False if system should halt
        """
        error_details = {
            'error': str(error),
            'error_type': type(error).__name__,
            'component': component,
            'context': context,
            'timestamp': time.time(),
            'traceback': traceback.format_exc()
        }
        
        try:
            self.error_count += 1
            
            # Classify error severity
            error_category = self._classify_error(error, context)
            error_details['category'] = error_category
            
            # Check if this is a critical error
            is_critical = self._is_critical_error(error, context, error_category)
            error_details['is_critical'] = is_critical
            
            if is_critical:
                return await self._handle_critical_error(error_details)
            else:
                return await self._handle_non_critical_error(error_details)
                
        except Exception as handler_error:
            # Error in error handler - this is critical
            self.critical_logger.error(f"❌ [FAIL-FAST-HANDLER] Error handler failed: {handler_error}")
            self.critical_logger.error(f"Original error: {error}")
            self.critical_logger.error(traceback.format_exc())
            
            if self.fail_fast_enabled:
                await self._halt_system("Error handler failure", error_details)
                return False
            
            return True
    
    def _classify_error(self, error: Exception, context: Dict[str, Any]) -> str:
        """Classify error into categories"""
        error_str = str(error).lower()
        
        for category, patterns in self.critical_patterns.items():
            for pattern in patterns:
                import re
                if re.search(pattern, error_str):
                    return category
        
        # Check context for error classification
        if 'api_call' in context and 'timeout' in error_str:
            return 'data_unavailable'
        elif 'balance' in context and 'fetch' in error_str:
            return 'balance_errors'
        elif 'trading' in context and 'order' in error_str:
            return 'trading_errors'
        
        return 'unknown'
    
    def _is_critical_error(self, error: Exception, context: Dict[str, Any],
                          error_category: str) -> bool:
        """Determine if error is critical and should halt trading"""
        # Check if this is a test context
        is_test_context = context.get('test_mode', False) or context.get('component', '') == 'test_component'

        # All validation failures are critical (except in test mode)
        if error_category == 'validation_failures':
            return not is_test_context

        # Data unavailability is critical for trading
        if error_category == 'data_unavailable' and context.get('trading_active', False):
            return True

        # Authentication failures are critical
        if error_category == 'authentication_failed':
            return True

        # Balance errors during trading are critical
        if error_category == 'balance_errors' and context.get('trading_operation', False):
            return True

        # Trading errors during active trading are critical
        if error_category == 'trading_errors' and context.get('live_trading', False):
            return True

        # Check for specific critical error types (but not in test mode)
        critical_error_types = [
            'RuntimeError', 'ConnectionError',
            'TimeoutError', 'AuthenticationError'
        ]

        if type(error).__name__ in critical_error_types:
            return not is_test_context

        # ValueError is only critical if not in test mode and in trading context
        if type(error).__name__ == 'ValueError':
            return not is_test_context and context.get('trading_active', False)

        return False
    
    async def _handle_critical_error(self, error_details: Dict[str, Any]) -> bool:
        """Handle critical error with fail-fast logic"""
        self.critical_errors.append(error_details)
        self.last_critical_error = time.time()
        
        # Log critical error
        self.critical_logger.error(f"❌ [CRITICAL-ERROR] {error_details['component']}: {error_details['error']}")
        self.critical_logger.error(f"Context: {json.dumps(error_details['context'], default=str)}")
        self.critical_logger.error(f"Traceback: {error_details['traceback']}")
        
        # Check if we've exceeded critical error threshold
        recent_critical_errors = [
            e for e in self.critical_errors 
            if time.time() - e['timestamp'] < 300  # Last 5 minutes
        ]
        
        if len(recent_critical_errors) >= self.critical_error_threshold:
            await self._halt_system("Critical error threshold exceeded", error_details)
            return False
        
        # Handle specific error categories
        error_category = error_details.get('category', 'unknown')
        if error_category in self.error_handlers:
            should_continue = await self.error_handlers[error_category](error_details)
            if not should_continue:
                return False
        
        # If fail-fast is enabled, halt on any critical error
        if self.fail_fast_enabled:
            await self._halt_system(f"Critical error: {error_details['error']}", error_details)
            return False
        
        return True
    
    async def _handle_non_critical_error(self, error_details: Dict[str, Any]) -> bool:
        """Handle non-critical error"""
        logger.warning(f"⚠️ [NON-CRITICAL-ERROR] {error_details['component']}: {error_details['error']}")
        
        # Log non-critical error for monitoring
        if self.error_count % 10 == 0:  # Log every 10th error
            logger.warning(f"⚠️ [ERROR-SUMMARY] Total errors: {self.error_count}")
        
        return True
    
    async def _handle_data_unavailable_with_recovery(self, error_details: Dict[str, Any]) -> bool:
        """Handle data unavailability errors with automatic recovery"""
        logger.error(f"❌ [DATA-UNAVAILABLE] {error_details['error']}")

        # Attempt data recovery instead of halting
        if self.recovery_mode_enabled and self.data_recovery_system:
            try:
                logger.info("🔄 [DATA-RECOVERY] Attempting to recover unavailable data...")

                # Extract data source from context
                context = error_details.get('context', {})
                data_source = context.get('data_source', 'unknown')

                # Attempt recovery
                recovery_success, recovered_data = await self.data_recovery_system.detect_and_recover_fake_data(
                    data_source, {}, ['Data unavailable']
                )

                if recovery_success:
                    logger.info("✅ [DATA-RECOVERY] Successfully recovered unavailable data")
                    return True
                else:
                    logger.warning("⚠️ [DATA-RECOVERY] Data recovery failed, continuing with caution")

            except Exception as e:
                logger.error(f"❌ [DATA-RECOVERY] Recovery attempt failed: {e}")

        # Fallback to original behavior only for critical trading operations
        context = error_details.get('context', {})
        if context.get('trading_active', False) and context.get('critical_operation', False):
            logger.warning("⚠️ [DATA-UNAVAILABLE] Critical trading operation - continuing with degraded data")
            return True  # Continue instead of halt

        return True
    
    async def _handle_authentication_failed(self, error_details: Dict[str, Any]) -> bool:
        """Handle authentication failures"""
        logger.error(f"❌ [AUTH-FAILED] {error_details['error']}")
        
        # Authentication failures always halt trading
        await self._halt_system("Authentication failed", error_details)
        return False
    
    async def _handle_balance_errors_with_recovery(self, error_details: Dict[str, Any]) -> bool:
        """Handle balance-related errors with automatic recovery"""
        logger.error(f"❌ [BALANCE-ERROR] {error_details['error']}")

        # Attempt balance data recovery
        if self.recovery_mode_enabled and self.data_recovery_system:
            try:
                logger.info("🔄 [BALANCE-RECOVERY] Attempting to recover balance data...")

                context = error_details.get('context', {})
                exchange = context.get('exchange', 'unknown')

                # Attempt to recover fresh balance data
                recovery_success, recovered_data = await self.data_recovery_system.detect_and_recover_fake_data(
                    f"{exchange}_balance_data", {}, ['Balance error detected']
                )

                if recovery_success:
                    logger.info("✅ [BALANCE-RECOVERY] Successfully recovered balance data")
                    return True
                else:
                    logger.warning("⚠️ [BALANCE-RECOVERY] Balance recovery failed")

            except Exception as e:
                logger.error(f"❌ [BALANCE-RECOVERY] Balance recovery attempt failed: {e}")

        # Continue trading with caution instead of halting
        context = error_details.get('context', {})
        if context.get('trading_operation', False):
            logger.warning("⚠️ [BALANCE-ERROR] Continuing trading with balance uncertainty")

        return True
    
    async def _handle_trading_errors_with_recovery(self, error_details: Dict[str, Any]) -> bool:
        """Handle trading execution errors with automatic recovery"""
        logger.error(f"❌ [TRADING-ERROR] {error_details['error']}")

        # Attempt trading connection recovery
        if self.recovery_mode_enabled and self.stream_manager:
            try:
                logger.info("🔄 [TRADING-RECOVERY] Attempting to recover trading connection...")

                context = error_details.get('context', {})
                exchange = context.get('exchange', 'unknown')

                # Attempt to restore trading stream
                if hasattr(self.stream_manager, 'detect_and_recover_source_failure'):
                    recovery_success = await self.stream_manager.detect_and_recover_source_failure(
                        f"{exchange}_trading", {'failure_type': 'trading_error', 'critical': True}
                    )

                    if recovery_success:
                        logger.info("✅ [TRADING-RECOVERY] Successfully recovered trading connection")
                        return True
                    else:
                        logger.warning("⚠️ [TRADING-RECOVERY] Trading recovery failed")

            except Exception as e:
                logger.error(f"❌ [TRADING-RECOVERY] Trading recovery attempt failed: {e}")

        # Continue with degraded trading capability instead of halting
        context = error_details.get('context', {})
        if context.get('live_trading', False):
            logger.warning("⚠️ [TRADING-ERROR] Continuing with degraded trading capability")

        return True
    
    async def _handle_validation_failures_with_recovery(self, error_details: Dict[str, Any]) -> bool:
        """Handle validation failures with automatic data purge and recovery"""
        logger.error(f"❌ [VALIDATION-FAILED] {error_details['error']}")

        # Attempt to purge fake data and recover real data
        if self.recovery_mode_enabled and self.purge_system and self.data_recovery_system:
            try:
                logger.info("🔄 [VALIDATION-RECOVERY] Attempting to purge fake data and recover real data...")

                context = error_details.get('context', {})
                data_source = context.get('data_source', 'unknown')

                # First, purge fake data
                if hasattr(self.purge_system, 'scan_and_purge_fake_data'):
                    fake_data = context.get('invalid_data', {})
                    cleaned_data, purge_report = await self.purge_system.scan_and_purge_fake_data(
                        {data_source: fake_data}, f"{data_source}_store"
                    )

                    logger.info(f"🗑️ [VALIDATION-RECOVERY] Purge completed: {purge_report}")

                # Then, recover real data
                recovery_success, recovered_data = await self.data_recovery_system.detect_and_recover_fake_data(
                    data_source, fake_data, ['Validation failure detected']
                )

                if recovery_success:
                    logger.info("✅ [VALIDATION-RECOVERY] Successfully purged fake data and recovered real data")
                    return True
                else:
                    logger.warning("⚠️ [VALIDATION-RECOVERY] Data recovery failed after purge")

            except Exception as e:
                logger.error(f"❌ [VALIDATION-RECOVERY] Validation recovery attempt failed: {e}")

        # Continue with caution instead of halting
        logger.warning("⚠️ [VALIDATION-FAILED] Continuing with validation warnings - monitoring data quality")
        return True
    
    async def _halt_system(self, reason: str, error_details: Dict[str, Any]):
        """Halt the trading system"""
        self.system_halted = True
        self.halt_reason = reason
        
        halt_message = f"🛑 [SYSTEM-HALT] Trading system halted: {reason}"
        
        # Log to all available loggers
        logger.critical(halt_message)
        self.critical_logger.critical(halt_message)
        self.critical_logger.critical(f"Error details: {json.dumps(error_details, default=str)}")
        
        # Print to console for immediate visibility
        print(f"\n{'='*80}")
        print(halt_message)
        print(f"Timestamp: {datetime.now().isoformat()}")
        print(f"Component: {error_details.get('component', 'unknown')}")
        print(f"Error: {error_details.get('error', 'unknown')}")
        print(f"{'='*80}\n")
        
        # Create halt file for external monitoring
        try:
            halt_file = Path("SYSTEM_HALTED.flag")
            with open(halt_file, 'w') as f:
                json.dump({
                    'halt_time': time.time(),
                    'reason': reason,
                    'error_details': error_details
                }, f, default=str, indent=2)
        except Exception as e:
            logger.error(f"❌ [HALT-FILE] Could not create halt file: {e}")
    
    def validate_no_placeholder_substitution(self, value: Any, value_type: str, 
                                           context: str) -> bool:
        """
        Validate that no placeholder values are being substituted
        
        Returns:
            bool: True if value is valid, False if placeholder detected
        """
        try:
            # Check numeric placeholders
            if isinstance(value, (int, float, Decimal)):
                value_float = float(value)
                
                if value_type in self.prohibited_placeholders:
                    if value_float in self.prohibited_placeholders[value_type]:
                        error_msg = f"Placeholder value detected: {value_type}={value_float} in {context}"
                        logger.error(f"❌ [PLACEHOLDER-DETECTED] {error_msg}")
                        
                        if self.fail_fast_enabled:
                            raise RuntimeError(f"CRITICAL: {error_msg}")
                        
                        return False
            
            # Check string placeholders
            elif isinstance(value, str):
                value_lower = value.lower()
                
                for placeholder in self.prohibited_placeholders['strings']:
                    if placeholder in value_lower:
                        error_msg = f"Placeholder string detected: '{value}' in {context}"
                        logger.error(f"❌ [PLACEHOLDER-DETECTED] {error_msg}")
                        
                        if self.fail_fast_enabled:
                            raise RuntimeError(f"CRITICAL: {error_msg}")
                        
                        return False
            
            return True
            
        except Exception as e:
            logger.error(f"❌ [PLACEHOLDER-VALIDATION] Validation error: {e}")
            if self.fail_fast_enabled:
                raise
            return False
    
    def is_system_halted(self) -> bool:
        """Check if system is currently halted"""
        return self.system_halted
    
    def get_halt_reason(self) -> Optional[str]:
        """Get reason for system halt"""
        return self.halt_reason
    
    async def attempt_recovery(self) -> bool:
        """Attempt to recover from halt state"""
        if not self.system_halted:
            return True
        
        logger.info("🔄 [RECOVERY] Attempting system recovery...")
        
        # Check if enough time has passed since last critical error
        if self.last_critical_error:
            time_since_error = time.time() - self.last_critical_error
            if time_since_error < self.error_recovery_timeout:
                logger.warning(f"⚠️ [RECOVERY] Recovery timeout not reached: {time_since_error:.1f}s < {self.error_recovery_timeout}s")
                return False
        
        # Clear error state
        self.system_halted = False
        self.halt_reason = None
        self.critical_errors = []
        
        # Remove halt file
        try:
            halt_file = Path("SYSTEM_HALTED.flag")
            if halt_file.exists():
                halt_file.unlink()
        except Exception as e:
            logger.warning(f"⚠️ [RECOVERY] Could not remove halt file: {e}")
        
        logger.info("✅ [RECOVERY] System recovery completed")
        return True
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """Get error handling statistics"""
        recent_errors = [
            e for e in self.critical_errors 
            if time.time() - e['timestamp'] < 3600  # Last hour
        ]
        
        return {
            'total_errors': self.error_count,
            'critical_errors_total': len(self.critical_errors),
            'critical_errors_recent': len(recent_errors),
            'system_halted': self.system_halted,
            'halt_reason': self.halt_reason,
            'last_critical_error': self.last_critical_error,
            'fail_fast_enabled': self.fail_fast_enabled,
            'critical_error_threshold': self.critical_error_threshold,
            'error_recovery_timeout': self.error_recovery_timeout
        }
